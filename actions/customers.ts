"use server";
import { prisma } from "@/lib/prisma";
import {
  ActionResponse,
  CustomerProps,
  CustomerNeedProps,
} from "@/lib/definitions";
import { auth } from "@/lib/auth";
import { padNeedsWithMatch } from "./customerNeeds";

// 创建客户的示例动作
export const createCustomerAction = async (
  customerData: CustomerProps,
): Promise<ActionResponse<CustomerProps>> => {
  const session = await auth();
  const currentUser = session?.user;

  if (!currentUser || !currentUser?.id) {
    return {
      success: false,
      message: "用户未登录",
    };
  }

  try {
    const newCustomer = await prisma.tllCustomer.create({
      data: {
        ...customerData,
        agentUserId: currentUser?.id as string,
        name: customerData.name, // 确保name字段不为null
        language: customerData.language || "", // 确保language字段不为null
        email: customerData.email || "", // 确保email字段不为null
      } as any,
    });

    return {
      success: true,
      data: newCustomer,
    };
  } catch (error) {
    console.error("🚨 创建客户时出错:", error);
    return {
      success: false,
      message: "无法创建客户",
    };
  }
};

// 获取客户的示例动作
export const fetchCustomersAction = async (
  id?: string,
): Promise<ActionResponse<CustomerProps[]>> => {
  const session = await auth();
  const currentUser = session?.user;

  if (!currentUser || !currentUser?.id) {
    return {
      success: false,
      message: "ユーザーはログインしていません",
    };
  }

  let whereParams =
    currentUser?.accessLevel && currentUser?.accessLevel < 99
      ? {
          agentUserId: currentUser?.id as string,
        }
      : ({} as any);

  if (id) {
    whereParams = {
      id: id,
    };
  }

  try {
    const customers = await prisma.tllCustomer.findMany({
      where: whereParams as any,
      include: {
        agentUser: true,
        needs: true,
      },
      orderBy: [
        {
          agentUserId: "desc",
        },
        {
          createdAt: "desc",
        },
      ],
    });

    const formattedCustomers = await Promise.all(
      customers.map(async (customer) => ({
        ...customer,
        name: customer.name || "",
        agentUserId: customer.agentUserId?.toString() || "",
        language: customer.language || "",
        email: customer.email || "",
        needs:
          (await Promise.all(
            customer.needs?.map(
              async (need) =>
                await padNeedsWithMatch(need as CustomerNeedProps),
            ),
          )) || [],
      })),
    );

    return {
      success: true,
      data: formattedCustomers,
    };
  } catch (error) {
    console.error("🚨 获取客户时出错:", error);
    return {
      success: false,
      message: "无法获取客户",
    };
  }
};

export const updateCustomerAction = async (
  customerData: CustomerProps,
): Promise<ActionResponse<CustomerProps>> => {
  try {
    console.log("customerData", customerData);

    const updatedCustomer = await prisma.tllCustomer.update({
      where: { id: customerData.id },
      data: customerData as any,
      include: {
        needs: true,
      },
    });

    if (updatedCustomer.needs.length > 0 && customerData.agentUserId) {
      // Only update needs' agentUserId if customer has an assigned agent
      // (TllCustomerNeed.agentUserId is not nullable, so we can't set it to null)
      updatedCustomer.needs.forEach(async (need) => {
        await prisma.tllCustomerNeed.update({
          where: { id: need.id },
          data: { agentUserId: customerData.agentUserId as string },
        });
      });
    }

    return {
      success: true,
      data: updatedCustomer,
    };
  } catch (error) {
    console.error("🚨 更新客户时出错:", error);
    return {
      success: false,
      message: "无法更新客户",
    };
  }
};
