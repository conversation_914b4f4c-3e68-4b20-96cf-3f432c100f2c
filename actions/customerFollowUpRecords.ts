"use server";

import { prisma } from "@/lib/prisma";
import { ActionResponse, CustomerFollowUpRecordProps } from "@/lib/definitions";
import { auth } from "@/lib/auth";

// Create follow-up record
export const createFollowUpRecordAction = async (
  followUpData: CustomerFollowUpRecordProps
): Promise<ActionResponse<CustomerFollowUpRecordProps>> => {
  const session = await auth();
  const currentUser = session?.user;

  if (!currentUser || !currentUser?.id) {
    return {
      success: false,
      message: "ユーザーはログインしていません",
    };
  }

  try {
    const newFollowUpRecord = await prisma.tllCustomerFollowUpRecord.create({
      data: {
        agentUserId: currentUser.id,
        customerId: followUpData.customerId,
        followUpDate: followUpData.followUpDate,
        comments: followUpData.comments,
      },
      include: {
        customer: true,
        agentUser: true,
      },
    });

    return {
      success: true,
      data: newFollowUpRecord as CustomerFollowUpRecordProps,
    };
  } catch (error) {
    console.error("🔥 フォローアップ記録作成エラー:", error);
    return {
      success: false,
      message: "フォローアップ記録を作成できませんでした",
    };
  }
};

// Fetch follow-up records
export const fetchFollowUpRecordsAction = async (): Promise<
  ActionResponse<CustomerFollowUpRecordProps[]>
> => {
  const session = await auth();
  const currentUser = session?.user;

  if (!currentUser || !currentUser?.id) {
    return {
      success: false,
      message: "ユーザーはログインしていません",
    };
  }

  try {
    // Only users with accessLevel >= 90 can see all records, others see only their own
    const whereParams =
      currentUser?.accessLevel && currentUser?.accessLevel >= 90
        ? {}
        : { agentUserId: currentUser.id };

    const followUpRecords = await prisma.tllCustomerFollowUpRecord.findMany({
      where: whereParams,
      include: {
        customer: true,
        agentUser: true,
      },
      orderBy: {
        followUpDate: "desc",
      },
    });

    return {
      success: true,
      data: followUpRecords as CustomerFollowUpRecordProps[],
    };
  } catch (error) {
    console.error("🔥 フォローアップ記録取得エラー:", error);
    return {
      success: false,
      message: "フォローアップ記録を取得できませんでした",
    };
  }
};

// Update follow-up record
export const updateFollowUpRecordAction = async (
  followUpData: CustomerFollowUpRecordProps
): Promise<ActionResponse<CustomerFollowUpRecordProps>> => {
  const session = await auth();
  const currentUser = session?.user;

  if (!currentUser || !currentUser?.id) {
    return {
      success: false,
      message: "ユーザーはログインしていません",
    };
  }

  try {
    const updatedFollowUpRecord = await prisma.tllCustomerFollowUpRecord.update({
      where: { id: followUpData.id },
      data: {
        followUpDate: followUpData.followUpDate,
        comments: followUpData.comments,
      },
      include: {
        customer: true,
        agentUser: true,
      },
    });

    return {
      success: true,
      data: updatedFollowUpRecord as CustomerFollowUpRecordProps,
    };
  } catch (error) {
    console.error("🔥 フォローアップ記録更新エラー:", error);
    return {
      success: false,
      message: "フォローアップ記録を更新できませんでした",
    };
  }
};

// Delete follow-up record
export const deleteFollowUpRecordAction = async (
  id: string
): Promise<ActionResponse<null>> => {
  const session = await auth();
  const currentUser = session?.user;

  if (!currentUser || !currentUser?.id) {
    return {
      success: false,
      message: "ユーザーはログインしていません",
    };
  }

  try {
    await prisma.tllCustomerFollowUpRecord.delete({
      where: { id },
    });

    return {
      success: true,
      data: null,
    };
  } catch (error) {
    console.error("🔥 フォローアップ記録削除エラー:", error);
    return {
      success: false,
      message: "フォローアップ記録を削除できませんでした",
    };
  }
};
