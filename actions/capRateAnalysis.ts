"use server";

import { prisma } from "@/lib/prisma";
import { ActionResponse } from "@/lib/definitions";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { calcCoordinateDistance } from "@/lib/helper/geoDistance";
import { executeWithRetry } from "@/lib/prismaUtils";
import { logger } from "@/lib/logger";
import { VALUATION_MAX_YEAR_RETRIEVAL } from "@/app/api/cron/constants";
import { postalCodeCache } from "@/lib/instances/nodeCache";
import dayjs from "dayjs";

export async function getCapRateAnalysisData({
  selectedOption,
}: {
  selectedOption: { label: string; value: number; type: string };
}): Promise<
  ActionResponse<{
    nearbyRecords: UserLambdaRecordProps[];
    centerCoordinates: { latitude: number; longitude: number } | null;
  }>
> {
  try {
    // Create cache key based on search parameters
    const cacheKey = `caprate-${selectedOption.type}-${selectedOption.value}`;

    // Check cache first (15 minute cache)
    const cachedResult = postalCodeCache.get(cacheKey);
    if (cachedResult) {
      logger.debug(`🔥 Cap Rate Analysis: Cache hit for ${cacheKey}`);
      return {
        success: true,
        data: cachedResult as any,
      };
    }

    let centerCoordinates: { latitude: number; longitude: number } | null =
      null;

    // Get center coordinates based on search type using retry utility
    if (selectedOption.type === "area") {
      const area = await executeWithRetry(async () => {
        return await prisma.geoArea.findUnique({
          where: { id: selectedOption.value?.toString() },
        });
      });
      if (area?.latitude && area?.longitude) {
        centerCoordinates = {
          latitude: area.latitude,
          longitude: area.longitude,
        };
      }
    } else if (selectedOption.type === "station") {
      const station = await executeWithRetry(async () => {
        return await prisma.geoRailwayStationGroup.findUnique({
          where: { id: selectedOption.value.toString() },
        });
      });
      if (station?.latitude && station?.longitude) {
        centerCoordinates = {
          latitude: station.latitude,
          longitude: station.longitude,
        };
      }
    } else if (selectedOption.type === "building") {
      const building = await executeWithRetry(async () => {
        return await prisma.proBuilding.findUnique({
          where: { id: selectedOption.value.toString() },
        });
      });
      if (building?.latitude && building?.longitude) {
        centerCoordinates = {
          latitude: building.latitude,
          longitude: building.longitude,
        };
      }
    } else if (selectedOption.type === "postalCode") {
      const postalCode = await executeWithRetry(async () => {
        return await prisma.geoPostalCode.findFirst({
          where: { postalCode: selectedOption.value.toString() },
        });
      });
      if (postalCode?.latitude && postalCode?.longitude) {
        centerCoordinates = {
          latitude: postalCode.latitude,
          longitude: postalCode.longitude,
        };
      }
    }

    if (!centerCoordinates) {
      return {
        success: false,
        message: "座標情報が見つかりません",
      };
    }

    // Search for BUILDING records with optimized query
    const SEARCH_RADIUS = 5000; // 5km in meters
    const RESULT_COUNT = 200; // More results for better analysis

    // Calculate bounding box for more efficient spatial query
    const EARTH_RADIUS = 6371000; // Earth radius in meters
    const latDelta = (SEARCH_RADIUS / EARTH_RADIUS) * (180 / Math.PI);
    const lngDelta =
      (SEARCH_RADIUS /
        (EARTH_RADIUS *
          Math.cos((centerCoordinates.latitude * Math.PI) / 180))) *
      (180 / Math.PI);

    const minLat = centerCoordinates.latitude - latDelta;
    const maxLat = centerCoordinates.latitude + latDelta;
    const minLng = centerCoordinates.longitude - lngDelta;
    const maxLng = centerCoordinates.longitude + lngDelta;

    let whereFilters = {
      longitude: {
        gte: minLng,
        lte: maxLng,
        not: null,
      },
      latitude: {
        gte: minLat,
        lte: maxLat,
        not: null,
      },
      recordType: "BUILDING",
      landRight: "所有権",
      updatedAt: {
        gte: dayjs().subtract(VALUATION_MAX_YEAR_RETRIEVAL, "year").toDate(),
      },
      yearlyIncome: { gt: 0 }, // Only records with income data for cap rate calculation
      price: { gt: 0 }, // Only records with price data
    } as any;

    const records = await executeWithRetry(async () => {
      return await prisma.tllUserLambdaRecord.findMany({
        where: whereFilters,
        select: {
          id: true,
          recordType: true,
          recordSubType: true,
          price: true,
          yearlyIncome: true,
          address: true,
          longitude: true,
          latitude: true,
          buildingSize: true,
          landSize: true,
          buildingBuiltYear: true,
          buildingMaterial: true,
          landRight: true,
          nearestStation: true,
          nearestStationWalkMinute: true,
          transport: true,
          updatedAt: true,
          createdAt: true,
          // Only include essential related data
          priceChanges: {
            select: {
              id: true,
              price: true,
              recordDate: true,
              company: {
                select: {
                  id: true,
                  fullName: true,
                },
              },
            },
            orderBy: {
              recordDate: "desc",
            },
            take: 5, // Limit to recent price changes
          },
          nearestStationGroup: {
            select: {
              id: true,
              // nameJa: true,
            },
          },
        },
        orderBy: [{ updatedAt: "desc" }, { price: "asc" }],
        take: RESULT_COUNT * 2, // Get more records to filter by distance
      });
    });

    // Calculate distances and filter by radius (optimized)
    let nearbyRecords: (UserLambdaRecordProps & { distance: number })[] = [];

    // Use more efficient distance calculation and early termination
    for (const record of records) {
      if (record.latitude && record.longitude) {
        const distance =
          calcCoordinateDistance(
            record.latitude,
            record.longitude,
            centerCoordinates!.latitude,
            centerCoordinates!.longitude,
          ) * 1000; // convert to meters

        if (distance <= SEARCH_RADIUS) {
          nearbyRecords.push({
            ...(record as any),
            distance,
          });

          // Early termination if we have enough results
          if (nearbyRecords.length >= RESULT_COUNT * 1.5) {
            break;
          }
        }
      }
    }

    // Sort by distance and limit results
    nearbyRecords.sort((a, b) => a.distance - b.distance);
    nearbyRecords = nearbyRecords.slice(0, RESULT_COUNT);

    logger.debug(
      `🔥 Cap Rate Analysis: Found ${nearbyRecords.length} records within ${SEARCH_RADIUS}m`,
    );

    const result = {
      nearbyRecords,
      centerCoordinates,
    };

    // Cache the result for 15 minutes
    postalCodeCache.set(cacheKey, result, 900); // 15 minutes = 900 seconds
    logger.debug(`🔥 Cap Rate Analysis: Cached result for ${cacheKey}`);

    return {
      success: true,
      data: result,
    };
  } catch (error) {
    logger.error("🔥 Cap Rate Analysis Error:", error);
    console.error("🔥 Cap Rate Analysis Error:", error);
    return {
      success: false,
      message: `データの取得に失敗しました: ${error instanceof Error ? error.message : "Unknown error"}`,
    };
  }
}
