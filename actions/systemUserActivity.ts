"use server";

import { prisma } from "@/lib/prisma";

import {
  SystemMetricProps,
  SystemUserActivityProps,
} from "@/lib/definitions/system";
import { ActionResponse } from "@/lib/definitions";
import { auth } from "@/lib/auth";
import { SystemUserActivityEventTypeEnum } from "@prisma/client";
import { headers } from "next/headers";

const prefixRules: string[] = [
  "/ex/search/history",
  "/ex/search",
  "/ex/valuation/history",
  "/ex/valuation",
  "/ex/rent",
  "/ex/fav",
  "/ex/need",
  "/ex",
  "/an/mansion",
  "/an/area",
  "/an/station",
  "/an/company",
  "/an",
  "/it",
  "/it/cf",
  "/it/chiban",
  "/it/insight",
  "/it/uketsuke",
  "/pa/customer",
  "/pa/post",
  "/pa",
  "/ad/dashboard",
  "/ad/rent",
  "/ad/bid",
  "/ad/sell",
  "/ad/keibaiSumifu",
  "/ad/sns",
  "/ad",
  "/su/dashboard",
  "/su/data/building",
  "/su/data/merge",
  "/su/data",
  "/su/users",
  "/su",
  "/my/account",
  "/my/payment",
  "/my",
  // for public
  "/",
  "/login",
  "/pricing",
  "/cd",
  "/pp",
  "/tos",
  "/blog",
  "/app",
];

export async function normalizeRoute(path: string): Promise<string | null> {
  path = path.replace(/\/$/, ""); // Remove trailing slash

  for (const prefix of prefixRules) {
    if (path === prefix) return prefix;
    if (path.startsWith(`${prefix}/`)) return `${prefix}/[subpath]`;
  }

  // 明确返回 null，方便日志追踪与 BI 过滤
  return null;
}

export async function createNewSystemUserActivityAction({
  data,
}: {
  data: SystemUserActivityProps;
}): Promise<ActionResponse<SystemUserActivityProps>> {
  const session = await auth();
  const userId = session?.user?.id || null;

  let whiteList = ["BUTTON_CLICK", "FORM_SUBMIT", "LOGIN", "LOGOUT"];

  const headersList = await headers();
  const userAgent = headersList.get("user-agent");
  const referer = headersList.get("referer");

  if (!whiteList.includes(data.eventType) || !data.route) {
    return {
      data: null,
      success: false,
      message: "Invalid event type or route",
    };
  }

  let dataToCreate = {
    ...(userId ? { userId } : {}),
    eventType: data.eventType as SystemUserActivityEventTypeEnum,
    route:
      data.route.indexOf("?") !== -1 ? data.route.split("?")[0] : data.route, // ✅ 修改为只取路由部分
    routeNormalized: await normalizeRoute(data.route),
    ...(data.eventMetadata ? { eventMetadata: data.eventMetadata } : {}),
    ...(userAgent ? { userAgent } : {}),
    ...(referer ? { referer } : {}),
  };

  await prisma.systemUserActivity.create({
    data: dataToCreate,
  });

  return {
    data: data,
    success: true,
    message: "Metric saved successfully",
  };
}

export async function getUserActivityAction(): Promise<
  ActionResponse<SystemUserActivityProps>
> {
  const userActivity = await prisma.systemUserActivity.findMany({
    orderBy: {
      createdAt: "desc",
    },
    include: {
      user: true,
    },
  });

  return {
    data: userActivity,
    success: true,
    message: "User activity fetched successfully",
  };
}
