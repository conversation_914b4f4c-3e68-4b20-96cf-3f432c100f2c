{"Common": {"loading": "Loading, please wait...", "usageReached": "Usage Limit Reached", "usageReachedDescription": "To continue using the feature, please consider upgrading your plan or wait until tomorrow.", "upgrade": "Upgrade"}, "AboutUrbalytics": {"title1": "Built on Insight. Driven by Impact.", "title2": "We are Urbalytics. This is our story.", "whoWeAre": "WHO WE ARE", "whoWeAreTitle": "About Urbalytics", "whoWeAreDescription1": "Urbalytics is the leading provider of data and analytics for the Japanese real estate market. We exist to empower investors, property managers, and businesses with the most accurate tools to succeed in the real estate investment space.", "whoWeAreDescription2": "We believe data isn't the destination — it's a starting point. Our suite of products is designed with our customers' solutions in mind. Whether you're looking to find your next investment property, analyze market trends, or optimize your rental portfolio, Urbalytics helps you make data-driven decisions in the Japanese real estate market.", "ourBackstory": "OUR BACKSTORY", "ourBackstoryTitle": "From Internal Tool to Industry Leader", "ourBackstoryDescription1": "Urbalytics was born from real estate investors' frustration with incomplete and outdated information. Our founders, experienced in both technology and real estate investment, recognized the Japanese market lacked comprehensive, real-time data analytics tools.", "ourBackstoryDescription2": "Starting as an internal tool, Urbalytics evolved into a comprehensive platform tracking millions of properties across Japan. Our machine learning algorithms process data from a combination of public sources, team research, and commercial reports. Today, we serve thousands of investors, property managers, and real estate professionals who rely on our insights in one of the world's most dynamic real estate markets.", "howItWorks": "HOW IT WORKS", "howItWorksTitle": "The Science Behind Urbalytics", "howItWorksDescription1": "Urbalytics tracks millions of properties across Japan's major markets including Tokyo, Osaka, and Kyoto. We collect and analyze data from a combination of public sources, proprietary research, and commercial reports. Our AI and machine learning models monitor market trends, property valuations, rental yields, and investment opportunities—processing over 100,000 data points daily to deliver comprehensive insights.", "howItWorksDescription2": "Our proprietary algorithms evaluate location, asset type, market conditions, and historical performance to generate accurate forecasts and investment recommendations. By combining data-driven rigor with real-time analytics, Urbalytics offers the most reliable platform for Japanese real estate decision-making."}, "Header": {"login": "<PERSON><PERSON>", "signUp": "Sign Up", "signUpForFree": "Start for Free", "menuFeature": "Features", "menuFeatureSearch": "Property Search", "menuFeatureSearchDetails": "Check past sales history and price trends", "menuFeatureRent": "Rentalizer", "menuFeatureRentDetails": "Quickly determine the fair rent and upside", "menuFeatureValuation": "Valuation", "menuFeatureValuationDetails": "Quickly estimate income properties, detached houses, and land", "menuFeatureMarketInsight": "Market Insight", "menuFeatureMarketInsightDetails": "Visualize average rental yields and price levels across areas on an interactive map", "menuResource": "Resources", "menuResourceBlog": "Blog", "menuResourceBlogMarket": "Market", "menuResourceBlogMarketDetails": "Latest trends and analysis information in the real estate market", "menuResourceBlogSpot": "Spot", "menuResourceBlogSpotDetails": "Detailed introduction of hot areas and spot information", "menuResourceBlogBukken": "Property", "menuResourceBlogBukkenDetails": "Detailed report on property information and investment analysis", "menuResourceBlogUrbalytics": "Urbalytics Guide", "menuResourceBlogUrbalyticsDetails": "Latest features and service information from Urbalytics", "menuResourceFreeTool": "Free Tools", "menuResourceFreeToolCF": "CF Calculation", "menuResourceFreeToolCFDetails": "Cash flow calculation tool for real estate investment. Easily analyze profitability.", "menuResourceFreeToolChiban": "Chiban Search", "menuResourceFreeToolChibanDetails": "Chiban search tool. Quickly check chiban information from address.", "menuResourceFreeToolCapRate": "Cap Rate Analysis", "menuResourceFreeToolCapRateDetails": "Instantly analyze and compare cap rate benchmarks by area. Support investment decisions.", "menuResourceGlossary": "Real Estate Investment Glossary", "menuResourceGlossaryDetails": "A collection of real estate investment terms", "menuBlog": "Blog", "menuPricing": "Pricing", "menuSolution": "Solution", "menuContactUs": "Contact Us"}, "Footer": {"tos": "Terms of Service", "pp": "Privacy Policy", "cd": "Disclosure of Specific Commercial Transactions", "app": "App Download", "blog": "Blog", "runBy": " is operator", "disclosure": "Urbalytics is not a service that provides investment advice or sells financial products. The information provided is reference information and does not encourage specific investment decisions.", "feature": "Features", "solution": "Solutions", "solutionShisankanri": "Asset Management Company", "solutionMinpaku": "Airbnb Operator", "solutionKaitori": "Real Estate Acquisition Company", "solutionChuukai": "Real Estate Brokerage Company", "solutionChintai": "Real Estate Rental Management Company", "solutionKinyuu": "Financial Institution Loan Officer", "resource": "Resources", "resourceBlogMarket": "Blog - Market", "resourceBlogSpot": "Blog - Spot Special", "resourceBlogBukken": "Blog - Property Spotlight", "resourceBlogUrbalytics": "Blog - Urbalytics Guide", "resourceCF": "CF Calculation Tool", "resourceMinpaku": "Airbnb Income Simulator", "resourceChiban": "Chiban Search Tool", "resourceCapRate": "Cap Rate Analysis Tool", "resourceGlossary": "Real Estate Investment Glossary", "support": "Support", "supportBookDemo": "Book a Demo", "supportStart": "Start for Free", "supportCommunity": "Community", "supportContact": "Contact Us", "supportApp": "App Download", "company": "Company", "companyAbout": "About Urbalytics", "companyPricing": "Pricing", "companyEvents": "Events", "companyOperatingCompany": "Operating Company", "companyCareers": "Careers", "companySignIn": "Sign In", "companySignUp": "Sign Up", "address": "Shinsencho 11-11 Eden Building 3F, Shibuya-ku, Tokyo 150-0043, Japan"}, "Menu": {"search": "Search", "searchMain": "Sales History ", "searchHistory": "View History", "searchFav": "Favorites", "searchValuation": "Valuation", "searchValuationHistory": "Valuation History", "rentValuation": "Rent Estimation", "analysis": "Analysis", "analysisMansion": "Mansion", "analysisStation": "Station", "analysisArea": "Area", "analysisCompany": "Company", "analysisCompanyFav": "Favorites", "tool": "Investment Tools", "toolWatch": "Watchlist", "cf": "CF Calculation", "minpaku": "Airbnb Income Simulator", "chiban": "Chiban Map", "caprate": "Cap Rate Analysis", "kensetsu": "Investment Insights Map", "uketsuke": "Property Registration Ledger", "tllAgent": "TLL Agent", "tllAgentReferralCode": "Referral Code", "tllAgentCustomer": "Customer Management", "tllAgentProposal": "Proposal Management", "admin": "Admin", "adminNew": "New Sale Properties", "adminRent": "New Rent Properties", "adminBid": "Bidding", "adminBidMetrics": "Bid Metrics", "adminBidChecklist": "Bid Checklist", "adminSell": "Sell Management", "adminKeibaiSumifu": "<PERSON><PERSON><PERSON>", "adminKeibaiSumifuWakeari": "Wakeari Properties", "adminSNS": "SNS", "adminSNSContent": "SNS Content", "adminSNSMetrics": "SNS Metrics", "adminSNSHotspot": "Hotspot", "adminSNSCreate": "Create", "adminSNSBlog": "Blog", "superAdmin": "SUPER Admin", "superAdminDashboard": "Dashboard", "superAdminDashboardUserActivity": "User Activity", "superAdminData": "Data Management", "superAdminBuilding": "Building Management", "superAdminOwnBuilding": "Own Building", "superAdminMerge": "Merge Records", "superAdminUser": "User Management", "usage": "Current Usage (Daily Update)", "usageSearch": "Property Price History", "usagePriceChangeHistory": "Price Change History Views", "usageRentSearch": "Rent Estimates", "usageValuation": "Valuation", "usageAnalysis": "Analysis Report", "support": "Support", "payment": "Payment"}, "Homepage": {"homepage": "Homepage", "titlePrefix": "1 Click", "title": "find out a property’s real worth", "scrollDown": "Scroll Down", "typedPrefix": "", "typed": {"options1": "How much did the seller buy this condo for?", "options2": "How many times was the price reduced for this multi-family building?", "options3": "Isn’t this land priced a bit too high?", "options4": "How much did similar properties nearby sell for?", "options5": "Is the rent too high for this investment property?"}, "description": "Hidden insights <u>only available at Urbalytics</u>", "blog": "Blog", "blogAll": "View All Blogs", "search": {"placeholder": "Search by location/station/building name...", "noResult": "No Result", "newlyCreatedCount": "New:", "priceChangeCount": "Price Change:", "unit": "properties", "valuation": "Valuation", "rent": "Rentalizer", "sale": "Sale History Search", "valuationIntro": "Supports property URL input, manual entry, and photo uploads. Anyone can easily perform a multi-angle quick valuation in under a minute, without worrying about persistent sales calls.", "valuationCta": "Free Valuation", "tools": "Investment Tools", "tools1Title": "Construction Plan Map", "tools1Description": "Query the construction plan of the property", "tools2Title": "Chiban Map", "tools2Description": "Query the chiban of the property", "tools3Title": "Cash Flow Simulator", "tools3Description": "Understand your true net income at a glance"}, "salesPoint": {"title": "Core Features", "freeToUse": "Free to use"}, "trustedBy": "Trusted by professionals from", "salesPoint1": {"title": "Sales History", "subtitle": "Instantly track past transactions and price changes", "description": "Based on over 10 years of listing and transaction data, Urbalytics organizes and visualizes property price changes and sales history, helping you quickly understand the background of any property.", "footNote": "Currently covers Tokyo and the Greater Tokyo Area, with plans to expand nationwide.", "footNote2": "For Free Tier, daily usage is limited. For details please refer to Pricing."}, "salesPoint2": {"title": "Undervaluation Checker", "subtitle": "Intuitively find high-value properties", "description": "Based on historical transaction data by area and property type, our proprietary algorithm estimates the market price. For each listing, the system displays both the discount rate (%) and discount amount (JPY), allowing users to instantly assess whether the asking price is above or below market value.", "footNote": "Valuation is based on comparable analysis, with different benchmarks applied by property type."}, "salesPoint3": {"title": "Self-Serve <PERSON>", "subtitle": "No sales pressure, complete in under 1 minute", "description": "Support for property URL input, manual entry, and photo uploads. Anyone can easily perform a multi-angle quick valuation in under a minute, without worrying about persistent sales calls."}, "salesPoint4": {"title": "Cash Flow Simulation", "subtitle": "Instantly see your true net income", "description": "By inputting the property price, annual income, taxes, repair costs, loan payments, and your own capital, the simulator automatically calculates your true cash earnings during the holding period and upon sale. The graph visualizes your income, expenses, net CF, and IRR over a 30-year period at a glance."}, "salesPoint5": {"title": "Area Market Map", "subtitle": "Discover new listings and price changes in real time", "description": "Visualize average rental yields and price levels across areas on an interactive map. Quickly explore newly listed and price-adjusted properties in your target zones to capture market opportunities without missing a beat."}, "salesPoint6": {"title": "Rental Upside Analyzer", "subtitle": "Identify rental growth potential and pricing risks", "description": "By comparing to nearby rental markets, you can instantly assess whether a property's rent is fair, overpriced, or undervalued. Includes multiple reference points—sales properties, whole buildings, and unit condos—to uncover rental upside or price risk."}, "stats": {"propertiesTracked": "Properties Tracked", "pastRecordLogged": "Past Record Logged", "valuationConducted": "Valuation Conducted"}, "faq": {"title": "Frequently Asked Questions", "question1": {"question": "What is Urbalytics?", "answer": "Urbalytics is a data analysis platform for real estate investment. It provides market analysis, price predictions, and risk assessments using AI to support better investment decisions."}, "question2": {"question": "What are the pricing plans for Urbalytics? Is it free to use?", "answer": "Urbalytics offers a Free plan, Plus plan, and Pro plan.Some features—such as transaction history search and property valuation—are available for free.For access to more detailed data analysis and full historical records, we recommend subscribing to one of our paid plans (starting from ¥50,000/year). An Enterprise plan with more advanced features is also available for corporate users. For full details, please refer to our pricing page."}, "question3": {"question": "How does appraisal work?", "answer": "AI appraisal analyzes past transaction data, market trends in the area, and prices of similar properties to calculate the estimated price of real estate."}, "question4": {"question": "How frequently is the data updated? What is the accuracy of the results?", "answer": "Our valuation system estimates property prices automatically using AI, based on tens of thousands of past transaction records, area characteristics, property structures, and market trends.However, since real estate prices are also influenced by factors that cannot be quantified digitally—such as sunlight exposure, interior condition, and maintenance status—the estimated price may differ from the actual closing price.Therefore, for those who wish to get a general sense of the market, we recommend using this valuation tool. For those who need a more precise estimate, we offer free individual consultations. Our experienced staff will review the details of your property and provide a valuation grounded in its actual condition."}, "question5": {"question": "Which areas' real estate data is supported?", "answer": "Currently, we support real estate data for the Greater Tokyo Area, with plans to add data from other prefectures in the future. Stay tuned!"}, "question6": {"question": "How is Urbalytics' data collected?", "answer": "Urbalytics’ data is built by combining public information, open data, internal transaction records, curated data from external research firms, and proprietary analytics. All data is properly processed and sanitized to ensure lawful and reliable use."}, "question7": {"question": "How is a 'closed sale' determined in the property sales history?", "answer": "We determine a 'closed sale' by cross-referencing the latest listing data with official registration records from the Legal Affairs Bureau. Please note that the actual sale price or terms may differ from the recorded information."}, "question8": {"question": "Is an API available?", "answer": "Currently, an API is available for some corporate customers. Please contact us if you would like to use the API."}, "question9": {"question": "Does Urbalytics provide investment advice?", "answer": "No. Urbalytics is a tool designed for visualizing and organizing information. It does not provide specific investment decisions or financial advice."}}}, "Login": {"welcome": "Welcome to Urbalytics", "noAccountNote": "Currently, new user registration is suspended. Please leave your email and we will contact you when registration is available in early May.", "noAccount": "Don't have an account?", "signUp": "Sign Up", "email": "Email", "emailPlaceholder": "<EMAIL>", "password": "Password", "passwordPlaceholder": "Enter your password", "forgotPassword": "Forgot Password?", "login": "<PERSON><PERSON>", "or": "Or", "continueWithGoogle": "Continue with Google", "termsAgreement": "By continuing to click, you agree to Urbalytic's <tos>Terms of Service</tos> and <pp>Privacy Policy</pp>.", "confirm": "Confirm", "signupEmailSent": "Signup email sent", "signupEmailSentDescription": "Please check your email for the verification link."}, "pricing": {"day": "Day", "month": "Month", "year": "Year", "times": "", "title": "Pricing Plan", "monthly": "Monthly", "yearly": "Yearly", "discount": "Discount", "free": "Free", "or": "Or", "whenYearly": "When Billed Yearly", "whenMonthly": "When Billed Monthly", "currentPlan": "Current Plan", "bestValue": "Best Value", "enterprisePlan": "Have questions about security, procurement, or custom contracts for corporate customers?", "aboutSales": "About Sales", "freeSubtitle": "Experience the basic features of Urbalytics for free", "plusSubtitle": "Utilize more advanced data analysis and reporting features", "proSubtitle": "Maximize the use of real estate information", "freeButtonText": "Start for Free", "plusButtonText": "Sign Up for Plus", "proButtonText": "Sign Up for Pro", "featureConstructionMap": "Construction Plan", "featureMinpaku": "Airbnb Income Simulator", "featureCFSimulation": "CF Simulation", "featureDetailsPage": "View Past Sales History", "featureValuation": "Valuation", "featureAnalysis": "Analysis Report", "featureNearbySeiyaku": "Neighborhood Sales Case", "featurePlus1": "Market Rent and Upside Analysis", "featurePlus2": "Price Change & Sales History", "featurePlus3": "Renting Case Search", "featurePlus4": "Property Discount Level & Amount", "featureCapRateChecker": "Cap Rate Checker", "featureCustom": "Custom Extraction Conditions", "featureSupport": "Individual Support", "featureFree": "All Features of the Free Plan", "featurePlus": "All Features of the Plus Plan", "featureCustomExtractionConditions": "Custom Extraction Conditions", "featurePropertyRegistrationLedger": "Property Registration Ledger", "featureIndividualSupport": "Individual Support", "featureRentSearch": "Rent Search", "featurePriceChangeHistory": "Price Change & Transaction History", "enterprise": "Enterprise", "unlimited": "Unlimited"}, "PricingDetail": {"freeButtonText": "Start for Free", "comparePlan": "Compare Pricing Plans", "enterprise": "Enterprise", "day": "Day", "month": "Month", "year": "Year", "times": "Times", "perDay": "times/day", "limit": "Limit", "detail": "Detail", "noSeiyaku": "No Sales Status", "onlyShousai": "Only Detail", "dateOnly": "Date Only", "noData": "No Data", "extraCost": "Extra Cost", "propertySearch": "Property Search & Analysis", "propertySearchDetail": "Sales History", "propertySearchHistory": "Price Change & Sales History", "propertySearchPriceChange": "Price Change History", "propertySearchDiscount": "Property Discount Level & Amount", "propertySearchRentUp": "Rental Upside Analyzer", "propertySearchFav": "Area Map Overview", "propertySearchFavCondition": "Investment Data Map", "propertySearchRent": "Renting Case Search", "propertySearchValuation": "Valuation", "propertySearchCsv": "Data CSV Output", "propertySearchAuction": "Auction Support", "marketInsight": "Investment Tools", "marketInsightDetail": "Land Number Search", "marketInsightApartment": "Cash Flow Simulator", "marketInsightStation": "Station Analysis", "marketInsightArea": "Area Analysis", "marketInsightCompany": "Company Analysis", "investmentTool": "Investment Tools", "investmentToolKensaku": "Development Plan Search", "investmentToolMap": "Investment Data Map", "investmentToolCf": "Cash Flow Simulator", "investmentToolMinpaku": "Airbnb Income Simulator", "investmentToolCapRate": "Cap Rate Checker", "investmentToolRegistration": "Property Registration Ledger", "investmentToolLoan": "Loan <PERSON> (2025Q3)", "investmentToolCustomize": "Custom Extraction Conditions (2025Q3)", "customSupport": "Custom Support", "customSupportProperty": "Property Sales Support", "customSupportIndividual": "Individual Support", "customSupportApi": "API Support", "customSupportCustomize": "Function & Data Customization"}, "Blog": {"title": "Blog", "allPosts": "All Posts", "author": "Author", "published": "Published", "toc": "Table of Contents", "goBack": "← Back to posts"}, "BlogPost": {"wordCount": "Words", "readingTime": "Estimated Reading Time", "minutes": "minutes", "getStarted": "Get started with Urbalytics today.", "getStartedDetails1": "With intuitive, data-rich tools, Urbalytics helps investors and brokers uncover hidden insights and outperform the market.", "getStartedButton": "Try for free", "relatedPosts": "Related Posts", "views": "Views", "copyright": "Copyright Notice: This article is original content by the author. Reproduction, copying, or quoting without permission is prohibited. Please contact the author or our team for usage inquiries."}, "General": {"noPermission": "Access Denied", "upgrade": "Upgrade Plan", "dailyLimitReached": "Daily Limit Reached", "orAddSupportStaff": "Or Contact Our Support Staff For Free Report"}, "Billing": {"title": "Billing", "currentPlan": "Current Plan", "nextBillingDate": "Next Billing Date", "subscriptionHistory": "Subscription History", "subscriptionChange": "Subscription Change", "manageSubscription": "Manage Subscription", "billing": "Billing", "billingHistory": "Billing History", "billingHistoryDescription": "Billing History", "billingHistoryDescription2": "Billing History", "billingHistoryDescription3": "Billing History"}, "Support": {"contactTantou": "<PERSON>", "tantouAdd": "<PERSON><PERSON>", "tantouAddDescription": "Add our support staff, free of charge, to help you generate reports and provide personalized support", "tantouAddDownload": "Download QR Code", "tantouAddDownloadDescription": "Download QR Code by scanning the QR code below.", "qrCode": "QR Code", "download": "Download", "title": "Support", "contactSupportStaff": "Contact Support Staff", "contactSupportStaffDescription": "Need help with subscription issues or technical support? Our support team is here to help. Please choose from the following contact methods:", "mail": "Email Consultation", "phone": "Phone Call", "noAnswerCheckWiki": "Can't find the answer? Check the wiki", "bookSession": "Book Private Consultation"}, "RentUtilities": {"tabLabels": {"rentSamePropertyKubun": "Same Property Rental", "rentSamePropertyKubunShort": "Same Rental", "rentKubun": "Nearby Rental Units", "rentKubunShort": "Rental Units", "rentHouse": "Nearby Rental Houses", "rentHouseShort": "Rental Houses", "rentIchibu": "Partial Rental", "rentIchibuShort": "Partial Rental", "rentAll": "All Rentals", "rentAllShort": "All Rentals", "saleRecords": "Sale Reference Cases", "saleRecordsShort": "Sale Cases"}, "messages": {"dataAutoReflect": "Data will be automatically reflected when entered", "estimateTooltip": "Estimated rent is calculated based on the average and 80th percentile of transaction data, excluding outliers.", "searchToShowResults": "Perform a search to display valuation results.", "dataLoading": "Loading data...", "noData": "No data"}}, "SolutionPage": {"title": "Solutions", "description": "Automate property discovery, CF calculations, and report generation. Fastest investment decisions and sales proposals", "heroTitle": "Systematize investment decisions and standardize processes. Maximize deal acquisition power with Urbalytics.", "heroDescription1": "Instantly build custom dashboards based on customer needs and investment criteria", "heroDescription2": "Consistently visualize and share trading, CF calculations, and KPI management", "heroDescription3": "Simultaneously strengthen internal operations and proposal accuracy", "heroDescription4": "Reduce back-office workload while improving deal response capabilities", "section1Label": "Custom Dashboard", "section1Title": "Organize all property information by criteria. Foundation for strategic decisions.", "section1Point1": "Bulk collection and listing of new and price-revised properties in the metropolitan area", "section1Point2": "Custom indicators according to investment criteria such as ROI and Upside", "section1Point3": "Support for follow-up and tracking by area, company, and person in charge", "section1Point4": "Support for sharing and progress management within internal teams", "section2Label": "Automated Proposal Report Generation", "section2Title": "Generate professional-level reports with just a few clicks", "section2Point1": "Automatically reflect quantitative data + qualitative analysis such as area information", "section2Point2": "Free editing possible, optimized for each customer's needs", "section2Point3": "Support for one-click PDF output and URL sharing", "section2Point4": "Instantly create persuasive proposal materials with over 10 pages of content", "section3Label": "Short-term Project Investment Simulation", "section3Title": "Visualize and calculate cash flow up to expected sale", "section3Point1": "Comprehensive simulation of purchase costs, financing conditions, and exit strategies", "section3Point2": "Automatically calculate bid prices by reverse calculation according to indicators such as ROI and net profit", "section3Point3": "Display costs, profits, and cash balances with graphs", "section3Point4": "Accelerate project-based investment decisions", "section4Label": "Bid Management and Centralization Tools", "section4Title": "Manage project status, contract conditions, and related documents end-to-end", "section4Point1": "Centrally manage purchase price, deposits, and response status by property", "section4Point2": "Prevent omissions with legal and rights-related checklists", "section4Point3": "Document upload and management functions that can be shared by teams", "section4Point4": "Optimal for establishing ToB-compatible workflows", "section5Label": "Cap Rate Analysis Tool", "section5Title": "Instantly analyze and compare cap rate benchmarks by area", "section5Point1": "Automatically calculate cap rate distribution from similar properties within 5km radius", "section5Point2": "Visualize market positioning with 20% and 80% percentiles", "section5Point3": "Detailed filtering by property type, building age, and price range", "section5Point4": "Objectively understand cap rate standards needed for investment decisions"}}