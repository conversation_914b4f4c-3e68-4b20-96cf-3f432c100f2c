generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("POSTGRES_URL") // Fix
  directUrl = env("POSTGRES_URL_NON_POOLING")
}

enum TllUserSubscriptionPlan {
  PLUS
  PRO
  ENTERPRISE
}

enum TllUserSubscriptionStatus {
  FREE
  ACTIVE
  PAST_DUE
  CANCELED
  TRIALING
}

model TllUser {
  id                String  @id @default(cuid())
  source            String  @db.VarChar(50)
  name              String? @db.VarChar(30)
  email             String? @db.VarChar(255)
  imageUrl          String? @map("image_url") @db.VarChar(255)
  language          String? @db.VarChar(10)
  wechatOpenId      String? @map("wechat_openid") @db.VarChar(255)
  phone             String? @db.VarChar(50)
  address           String? @db.VarChar(200)
  accessLevel       Int     @map("access_level")
  userSetting       Json?   @map("user_setting")
  emailSubscription Json?   @map("email_subscription")

  password String? @db.Var<PERSON><PERSON>(255)

  lastLoginAt  DateTime? @map("last_login_at") @db.Timestamp(6)
  loginHistory Json?     @map("login_history")

  stripeCustomerId   String?                   @map("stripe_customer_id") @db.Text
  subscriptionPlan   TllUserSubscriptionPlan?  @map("subscription_plan")
  subscriptionStatus TllUserSubscriptionStatus @default(FREE) @map("subscription_status")

  createdAt DateTime? @default(now()) @map("created_at")
  updatedAt DateTime? @updatedAt @map("updated_at")

  favMaps                 TllUsersUserLambdaRecordsFavMap[]
  aiSnsContents           AiSnsContent[]
  bids                    TllUserLambdaRecordBid[]
  searchHistories         TllUserLambdaRecordSearchHistory[]
  valuationRecords        ValuationRecord[]
  customers               TllCustomer[]
  customerNeeds           TllCustomerNeed[]
  customerFollowUpRecords TllCustomerFollowUpRecord[]
  pushSubscriptions       TllUserPushSubscription[]
  materialMappings        PropertyMaterialNameMapping[]
  systemReportViewHistory SystemReportViewHistory[]
  systemUserActivity      SystemUserActivity[]
  subscriptions           TllUserSubscription[]
  blogs                   Blog[]
  propertyWatchedCriteria PropertyWatchedCriteria[]          @ignore

  // 我创建的邀请码（我作为 agent）
  referralCodeId        String?   @map("referral_code_id") @db.VarChar(255)
  referralCodeExpiresAt DateTime? @map("referral_code_expires_at") @db.Timestamp(6)

  referralCode         ReferralCode?  @relation("referral_code_users", fields: [referralCodeId], references: [id])
  createdReferralCodes ReferralCode[] @relation("referral_code_created_by_user")

  @@unique(email)
  @@index([accessLevel, createdAt]) // for su/dashboard
  @@map("tll_users")
}

enum TllUserSubscriptionInterval {
  MONTHLY
  YEARLY
}

model TllUserSubscription {
  id String @id @default(cuid())

  userId               String                      @map("user_id")
  stripeSubscriptionId String                      @map("stripe_subscription_id")
  amount               Int                         @map("amount")
  interval             TllUserSubscriptionInterval @map("interval")

  subscriptionPlan    TllUserSubscriptionPlan   @map("subscription_plan")
  subscriptionStatus  TllUserSubscriptionStatus @map("subscription_status")
  subscriptionStartAt DateTime                  @map("subscription_start_at")
  subscriptionEndAt   DateTime                  @map("subscription_end_at")
  comments            Json?                     @map("comments")

  createdAt DateTime? @default(now()) @map("created_at")
  updatedAt DateTime? @updatedAt @map("updated_at")

  user TllUser @relation(fields: [userId], references: [id])

  @@map("tll_user_subscriptions")
}

enum TllUserActivationTokenType {
  PASSWORD_RESET
  EMAIL_VERIFICATION
}

model TllUserActivationToken {
  id String @id @default(cuid())

  email     String                     @map("email")
  tokenType TllUserActivationTokenType @map("token_type")
  token     String                     @unique @map("token")
  expiresAt DateTime                   @map("expires_at")

  used      Boolean  @default(false) @map("used")
  createdAt DateTime @default(now()) @map("created_at")

  @@map("tll_user_activation_tokens")
}

model TllUserPushSubscription {
  id           String   @id @default(cuid())
  userId       String   @map("user_id")
  deviceId     String   @map("device_id") // Unique identifier per device
  subscription Json     @map("subscription")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  user TllUser @relation(fields: [userId], references: [id])

  @@unique([userId, deviceId])
  @@map("tll_user_push_subscription")
}

model TllCustomer {
  id          String @id @default(cuid())
  name        String @db.VarChar(255)
  agentUserId String? @map("agent_user_id")

  salePriority Int? @map("sale_priority")

  language      String? @db.VarChar(10)
  email         String? @db.VarChar(255)
  wechatAccount String? @map("wechat_account") @db.VarChar(255)
  phone         String? @db.VarChar(50)
  address       String? @db.VarChar(200)
  age           Int?

  income   Int?
  jobVisa  String?
  comments String?

  mainNeedType TllCustomerMainNeedType @default(INVESTMENT_BUILDING) @map("main_need_type")
  source       TllCustomerSource       @default(TLL_REFERAL) @map("source")

  createdAt DateTime? @default(now()) @map("created_at")
  updatedAt DateTime? @updatedAt @map("updated_at")

  needs        TllCustomerNeed[]
  followUpRecords TllCustomerFollowUpRecord[]
  agentUser    TllUser?          @relation(fields: [agentUserId], references: [id])

  @@map("tll_customers")
}

enum TllCustomerMainNeedType {
  RENTAL
  INVESTMENT_BUILDING
  INVESTMENT_UNIT
  INVESTMENT_HOUSE
  INVESTMENT_LAND
  OWNER_OCCUPIED_UNIT
  OWNER_OCCUPIED_HOUSE
  OWNER_OCCUPIED_LAND
}

enum TllCustomerSource {
  URBALYTICS
  TLL_REFERAL
  AGENT_REFERAL
  RAKUMACHI
  KENBIYA
  ATHOME_SUUMO
  XIAOHONGSHU
  WECHAT_GROUP
}

enum TllCustomerNeedCriteriaType {
  POSTAL_CODE
  AREA_CODE
  STATION_GROUP_ID
  BUILDING_ID
}

model TllCustomerNeed {
  id             String  @id @default(cuid())
  customerUserId String? @map("customer_user_id")
  priority       Int?
  agentUserId    String  @map("agent_user_id")

  title      String?                       @map("title") @db.Text
  recordType TllUserLambdaRecordRecordType @map("record_type")
  priceFrom  Int?                          @map("price_from")
  priceTo    Int?                          @map("price_to")
  roiFrom    Float?                        @map("roi_from")

  criteriaType           TllCustomerNeedCriteriaType @map("criteria_type")
  postalCodes            String?                     @map("postal_codes")
  areaCodes              String?                     @map("area_codes")
  nearestStationGroupIds String?                     @map("nearest_station_group_ids")
  buildingIds            String?                     @map("building_ids")

  nearestStationWalkMinuteTo Int? @map("nearest_station_walk_minute_to")

  landAreaFrom     Int?    @map("land_area_from")
  landAreaTo       Int?    @map("land_area_to")
  buildingAreaFrom Int?    @map("building_area_from")
  buildingAreaTo   Int?    @map("building_area_to")
  buildingMaterial String? @map("building_material") @db.VarChar(100)
  yearFrom         Int?    @map("year_from")

  landCanHotel Int?    @map("land_can_hotel")
  isFollow     Int?    @map("is_follow")
  comment      String?

  createdAt DateTime? @default(now()) @map("created_at")
  updatedAt DateTime? @updatedAt @map("updated_at")

  customer  TllCustomer? @relation(fields: [customerUserId], references: [id])
  agentUser TllUser?     @relation(fields: [agentUserId], references: [id])

  @@map("tll_customer_needs")
}

model TllCustomerFollowUpRecord {
  id           String    @id @default(cuid())
  agentUserId  String    @map("agent_user_id")
  customerId   String    @map("customer_id")
  followUpDate DateTime  @map("follow_up_date")
  comments     String?   @db.Text

  createdAt DateTime? @default(now()) @map("created_at")
  updatedAt DateTime? @updatedAt @map("updated_at")

  customer  TllCustomer @relation(fields: [customerId], references: [id])
  agentUser TllUser     @relation(fields: [agentUserId], references: [id])

  @@map("tll_customer_follow_up_records")
}

enum TllUserLambdaRecordRecordType {
  BUILDING
  LAND
  MANSION
  HOUSE
}

model ValuationRecord {
  id                         String                        @id @default(cuid())
  userId                     String                        @map("user_id")
  recordType                 TllUserLambdaRecordRecordType @map("record_type")
  compositeTitle             String?                       @map("composite_title") @db.VarChar(100)
  price                      Int?                          @map("price")
  yearlyIncome               Float?                        @map("yearly_income")
  transport                  String?                       @map("transport") @db.VarChar(255)
  nearestStation             String?                       @map("nearest_station") @db.VarChar(255)
  nearestStationGroupId      String?                       @map("nearest_station_group_id") @db.VarChar(255)
  nearestStationWalkMinute   Int?                          @map("nearest_station_walk_minute")
  valueRosenka               Int?                          @map("value_rosenka")
  prefectureCode             Int?                          @map("prefecture_code")
  areaCode                   Int?                          @map("area_code")
  postalCode                 String?                       @map("postal_code")
  address                    String?                       @map("address")
  longitude                  Float?                        @map("longitude")
  latitude                   Float?                        @map("latitude")
  landSize                   Float?                        @map("land_size")
  landRight                  String?                       @map("land_right") @db.VarChar(100)
  buildingSize               Float?                        @map("building_size")
  buildingName               String?                       @map("building_name") @db.VarChar(255)
  buildingMaterial           String?                       @map("building_material") @db.VarChar(100)
  buildingLayout             String?                       @map("building_layout") @db.VarChar(100)
  buildingBuiltYear          Int?                          @map("building_built_year")
  buildingLevel              String?                       @map("building_level")
  buildingRoomCount          Int?                          @map("building_room_count")
  landType                   String?                       @map("land_type") @db.VarChar(50)
  landBuildingCoverageRatio  Int?                          @map("land_building_coverage_ratio")
  landFloorAreaRatio         Int?                          @map("land_floor_area_ratio")
  roadConnection             String?                       @map("road_connection") @db.VarChar(100)
  roadConnectionFirstType    String?                       @map("road_connection_first_type") @db.VarChar(100)
  roadConnectionFirstFacing  String?                       @map("road_connection_first_facing") @db.VarChar(100)
  roadConnectionFirstWidth   String?                       @map("road_connection_first_width")
  roadConnectionSecondType   String?                       @map("road_connection_second_type") @db.VarChar(100)
  roadConnectionSecondFacing String?                       @map("road_connection_second_facing") @db.VarChar(100)
  roadConnectionSecondWidth  String?                       @map("road_connection_second_width")
  analysisSimulationConfig   Json?                         @map("analysis_simulation_config")
  analysisSimulationResults  Json?                         @map("analysis_simulation_results")
  salesComments              String?                       @map("sales_comments")

  valuationDate DateTime  @default(now()) @map("valuation_date") @db.Date
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime? @updatedAt @map("updated_at")

  nearestStationGroup    GeoRailwayStationGroup? @relation(fields: [nearestStationGroupId], references: [id])
  user                   TllUser?                @relation(fields: [userId], references: [id])
  propertyAnalysisResult PropertyAnalysisResult?

  @@index([valuationDate])
  @@index([recordType, nearestStation])
  @@index([userId, valuationDate]) // For su/dashboard
  @@map("valuation_records")
}

model TllUserLambdaRecord {
  id String @id @default(cuid())

  sourceData    String?                       @map("source_data") @db.VarChar(50)
  recordType    TllUserLambdaRecordRecordType @map("record_type")
  recordSubType String?                       @map("record_sub_type") @db.VarChar(100)

  compositeTitle String? @map("composite_title") @db.VarChar(100)
  price          Int?    @map("price")
  yearlyIncome   Float?  @map("yearly_income")

  transport                String? @map("transport") @db.VarChar(255)
  nearestStation           String? @map("nearest_station") @db.VarChar(255)
  nearestStationGroupId    String? @map("nearest_station_group_id") @db.VarChar(255)
  nearestStationWalkMinute Int?    @map("nearest_station_walk_minute")

  prefectureCode Int?   @map("prefecture_code")
  areaCode       Int?   @map("area_code")
  postalCode     Int?   @map("postal_code")
  address        String @map("address")
  longitude      Float? @map("longitude")
  latitude       Float? @map("latitude")
  valueRosenka   Int?   @map("value_rosenka")

  landSize                   Float?  @map("land_size")
  landRight                  String? @map("land_right") @db.VarChar(100)
  landType                   String? @map("land_type") @db.VarChar(50)
  landBuildingCoverageRatio  Int?    @map("land_building_coverage_ratio")
  landFloorAreaRatio         Int?    @map("land_floor_area_ratio")
  roadConnection             String? @map("road_connection") @db.VarChar(100)
  roadConnectionFirstType    String? @map("road_connection_first_type") @db.VarChar(100)
  roadConnectionFirstFacing  String? @map("road_connection_first_facing") @db.VarChar(100)
  roadConnectionFirstWidth   String? @map("road_connection_first_width")
  roadConnectionSecondType   String? @map("road_connection_second_type") @db.VarChar(100)
  roadConnectionSecondFacing String? @map("road_connection_second_facing") @db.VarChar(100)
  roadConnectionSecondWidth  String? @map("road_connection_second_width")

  buildingId        String? @map("building_id")
  buildingName      String? @map("building_name") @db.VarChar(255)
  buildingSize      Float?  @map("building_size")
  buildingMaterial  String? @map("building_material") @db.VarChar(100)
  buildingLayout    String? @map("building_layout") @db.VarChar(100)
  buildingBuiltYear Int?    @map("building_built_year")
  buildingLevel     String? @map("building_level")
  buildingRoomCount Int?    @map("building_room_count")

  recordValues              Json?     @map("record_values")
  salesComments             String?   @map("sales_comments")
  analysisSimulationConfig  Json?     @map("analysis_simulation_config")
  analysisSimulationResults Json?     @map("analysis_simulation_results")
  createdAt                 DateTime? @default(now()) @map("created_at")
  updatedAt                 DateTime? @updatedAt @map("updated_at")

  priceChanges     TllUserLambdaRecordPriceChange[]
  favMaps          TllUsersUserLambdaRecordsFavMap[]
  sumitomoAuctions ProRawSumitomoAuction[]
  bids             TllUserLambdaRecordBid[]
  searchHistories  TllUserLambdaRecordSearchHistory[]
  materialMappings PropertyMaterialNameMapping[]

  nearestStationGroup    GeoRailwayStationGroup? @relation(fields: [nearestStationGroupId], references: [id])
  building               ProBuilding?            @relation(fields: [buildingId], references: [id])
  propertyAnalysisResult PropertyAnalysisResult?
  property               TllProperty?

  @@unique([compositeTitle, recordType])
  @@index([recordType, buildingId])
  @@index([recordType, postalCode])
  @@index([recordType, areaCode])
  @@index([recordType, nearestStationGroupId])
  @@index([recordType, prefectureCode, nearestStationGroupId, longitude, latitude]) // for finding nearby during analysis
  @@index([recordType, landRight, yearlyIncome, price, longitude, latitude, updatedAt]) // for cap rate analysis
  @@index([postalCode])
  @@index([areaCode])
  @@index([nearestStationGroupId])
  @@index([price, landSize, buildingSize, buildingBuiltYear]) // for search
  @@index([updatedAt])
  @@map("tll_user_lambda_records")
}

model TllUserLambdaRecordSearchHistory {
  id String @id @default(cuid())

  userId   String  @map("user_id")
  recordId String  @map("record_id")
  isValid  Boolean @default(true) @map("is_valid")

  searchDate DateTime @map("search_date") @db.Date

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  tllUserLambdaRecord TllUserLambdaRecord? @relation(fields: [recordId], references: [id])
  tllUser             TllUser?             @relation(fields: [userId], references: [id])

  @@index([userId])
  @@index([searchDate])
  @@index([userId, recordId, isValid])
  @@index([userId, searchDate]) // For su/dashboard
  @@map("tll_user_lambda_record_search_histories")
}

enum TllUserLambdaRecordBidStatus {
  ONE_REQUEST_DATA
  TWO_NEGOTIATION
  THREE_DOCUMENT_PROPERTY_CHECK
  FOUR_CONTRACT
  FIVE_HANDOVER
  NINE_CANCEL
}

model TllUserLambdaRecordBid {
  id               String                       @id @default(cuid())
  recordId         String                       @map("record_id")
  recordPrice      Int                          @map("record_price")
  status           TllUserLambdaRecordBidStatus @default(ONE_REQUEST_DATA) @map("status")
  salesUserId      String                       @map("sales_user_id")
  isSumitomoKeibai Boolean                      @default(false) @map("is_sumitomo_keibai")
  biddingPrice     Int?                         @map("bidding_price")
  biddingResult    Int?                         @map("bidding_result")
  checklist        Json?                        @map("checklist")
  comments         String?                      @map("comments") @db.Text
  dataLink         String?                      @map("data_link") @db.VarChar(255)
  createdAt        DateTime?                    @default(now()) @map("created_at")
  updatedAt        DateTime?                    @updatedAt @map("updated_at")

  tllUserLambdaRecord TllUserLambdaRecord? @relation(fields: [recordId], references: [id])
  salesUser           TllUser?             @relation(fields: [salesUserId], references: [id])

  @@map("tll_user_lambda_record_bids")
}

enum TllUserLambdaRecordPriceChangeSourceType {
  REINS
  SUUMO
  RAKUMACHI
  SITE_NOMU
  SITE_SUMITOMO
  SITE_LIVABLE
  SITE_MITSUI
  SITE_MITSUBISHI
  KEIBAI_SUMITOMO
}

model TllUserLambdaRecordPriceChange {
  id         String   @id @default(cuid())
  recordId   String?  @map("record_id")
  recordDate DateTime @map("record_date") @db.Date

  source TllUserLambdaRecordPriceChangeSourceType @map("source")

  status       String @map("status") @db.VarChar(255)
  price        Int?   @map("price")
  yearlyIncome Int?   @map("yearly_income")

  companyId String? @map("company_id")

  brokerType   String? @map("broker_type") @db.VarChar(255)
  reinsNumber  String? @map("reins_number") @db.VarChar(255)
  canAdvertise String? @map("can_advertise") @db.VarChar(255)
  chirashiLink String? @map("chirashi_link")
  chirashiType String? @map("chirashi_type") @db.VarChar(32)
  comments     String? @map("comments")

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  tllUserLambdaRecord TllUserLambdaRecord? @relation(fields: [recordId], references: [id])
  company             ProCompany?          @relation(fields: [companyId], references: [id])

  @@index([recordDate, recordId, status, price]) // for daily chekcup portal site
  @@map("tll_user_lambda_record_price_changes")
}

model ProCompany {
  id String @id @default(cuid())

  fullName      String? @map("full_name") @db.VarChar(500)
  companyName   String? @map("company_name") @db.VarChar(255)
  branchName    String? @map("branch_name") @db.VarChar(255)
  address       String? @map("address") @db.VarChar(255)
  repName       String? @map("rep_name") @db.VarChar(100)
  contactNumber String? @map("contact_number") @db.VarChar(100)
  faxNumber     String? @map("fax_number") @db.VarChar(100)
  email         String? @map("email") @db.VarChar(255)
  licenseNumber String? @map("license_number") @db.VarChar(100)
  url           String? @map("url") @db.VarChar(100)
  isFav         Int?    @map("is_fav")
  comments      String? @map("comments")

  createdAt DateTime? @default(now()) @map("created_at")
  updatedAt DateTime? @updatedAt @map("updated_at")

  priceChanges TllUserLambdaRecordPriceChange[]

  @@index([fullName])
  @@map("pro_companies")
}

model GeoPrefecture {
  code      Int       @id
  name      String?   @db.VarChar(50)
  longitude Float?
  latitude  Float?
  createdAt DateTime? @default(now()) @map("created_at")
  updatedAt DateTime? @updatedAt @map("updated_at")

  areas           GeoArea[]
  cities          GeoCity[]
  postalCodes     GeoPostalCode[]
  railwayStations GeoRailwayStation[]
  // GeoCity           GeoCity[]
  // GeoPostalCode     GeoPostalCode[]
  // GeoRailwayStation GeoRailwayStation[]

  @@map("geo_prefectures")
}

model GeoArea {
  id     String  @id @default(cuid())
  cityId String? @map("city_id")

  prefectureCode Int     @map("prefecture_code")
  code           Int     @unique
  nameJa         String? @map("name_ja") @db.VarChar(50)
  nameKatakana   String? @map("name_katakana") @db.VarChar(50)

  longitude Float?    @map("longitude")
  latitude  Float?    @map("latitude")
  createdAt DateTime? @default(now()) @map("created_at")
  updatedAt DateTime? @updatedAt @map("updated_at")

  prefecture  GeoPrefecture   @relation(fields: [prefectureCode], references: [code])
  city        GeoCity?        @relation(fields: [cityId], references: [id])
  postalCodes GeoPostalCode[]
  blogs       Blog[]          @relation("related_area")

  @@index([prefectureCode, nameJa])
  @@map("geo_areas")
}

model GeoCity {
  id             String    @id @default(cuid())
  prefectureCode Int       @map("prefecture_code")
  name           String?   @db.VarChar(50)
  nameKatakana   String?   @map("name_katakana") @db.VarChar(50)
  longitude      Float?    @map("longitude")
  latitude       Float?    @map("latitude")
  createdAt      DateTime? @default(now()) @map("created_at")
  updatedAt      DateTime? @updatedAt @map("updated_at")

  prefecture  GeoPrefecture   @relation(fields: [prefectureCode], references: [code])
  areas       GeoArea[]
  postalCodes GeoPostalCode[]

  @@map("geo_cities")
}

// Below is run in database 
// ALTER TABLE geo_postal_codes
// ADD COLUMN search_text tsvector GENERATED ALWAYS AS (
//   to_tsvector('simple',
//     coalesce(prefecture_name, '') || ' ' ||
//     coalesce(city_name, '') || ' ' ||
//     coalesce(area_name, '') || ' ' ||
//     coalesce(choume_name, '')
//   )
// ) STORED;

// CREATE INDEX idx_geo_postal_codes_search_text
// ON geo_postal_codes
// USING GIN (search_text);

model GeoPostalCode {
  id                   String                   @id @default(cuid())
  postalCode           String                   @map("postal_code")
  prefectureCode       Int                      @map("prefecture_code")
  prefectureName       String?                  @map("prefecture_name") @db.VarChar(255)
  cityCode             String?                  @map("city_code")
  cityName             String?                  @map("city_name") @db.VarChar(255)
  areaCode             Int?                     @map("area_code")
  areaName             String?                  @map("area_name") @db.VarChar(255)
  choumeName           String?                  @map("choume_name")
  buildingAveragePrice Int?                     @map("building_average_price")
  buildingRecordCount  Int?                     @map("building_record_count")
  buildingRecordRoi    Float?                   @map("building_record_roi")
  houseRecordCount     Int?                     @map("house_record_count")
  houseAveragePrice    Int?                     @map("house_average_price")
  landRecordCount      Int?                     @map("land_record_count")
  landAveragePrice     Int?                     @map("land_average_price")
  mansionRecordCount   Int?                     @map("mansion_record_count")
  mansionAveragePrice  Int?                     @map("mansion_average_price")
  search_text          Unsupported("tsvector")? @default(dbgenerated("to_tsvector('simple'::regconfig, (((((((COALESCE(prefecture_name, ''::character varying))::text || ' '::text) || (COALESCE(city_name, ''::character varying))::text) || ' '::text) || (COALESCE(area_name, ''::character varying))::text) || ' '::text) || COALESCE(choume_name, ''::text)))"))

  longitude Float? @map("longitude")
  latitude  Float? @map("latitude")

  createdAt DateTime? @default(now()) @map("created_at")
  updatedAt DateTime? @updatedAt @map("updated_at")

  prefecture              GeoPrefecture             @relation(fields: [prefectureCode], references: [code])
  city                    GeoCity?                  @relation(fields: [cityCode], references: [id])
  area                    GeoArea?                  @relation(fields: [areaCode], references: [code])
  addresses               GeoAddress[]
  systemReportViewHistory SystemReportViewHistory[]
  proProjects             ProProject[]
  blogs                   Blog[]                    @relation("related_postal_codes")

  @@index([postalCode])
  @@map("geo_postal_codes")
}

model GeoAddress {
  id      String @id @default(cuid())
  pcode   Int    @map("postal_code")
  address String @map("address") @db.VarChar(500)

  chibanAddress String? @map("chiban_address") @db.VarChar(500)
  chibanDetails Json?   @map("chiban_details")

  longitude Float? @map("longitude")
  latitude  Float? @map("latitude")

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Cannot map because it is not unique
  postalCodes GeoPostalCode[]

  @@unique([address])
  @@map("geo_addresses")
}

model GeoChiban {
  id             String   @id @default(cuid())
  name           String   @map("name") @db.VarChar(50)
  prefectureCode Int      @map("prefecture_code")
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")

  @@map("geo_chiban")
}

model GeoRailwayStationGroup {
  id                      String  @id @default(cuid())
  name                    String? @db.VarChar(50)
  prefectureCode          Int     @map("prefecture_code")
  postalCode              String? @map("postal_code") @db.VarChar(50)
  address                 String? @db.VarChar(100)
  longitude               Float
  latitude                Float
  stationUserCombined2011 Int?    @map("station_user_combined_2011")
  stationUserCombined2012 Int?    @map("station_user_combined_2012")
  stationUserCombined2013 Int?    @map("station_user_combined_2013")
  stationUserCombined2014 Int?    @map("station_user_combined_2014")
  stationUserCombined2015 Int?    @map("station_user_combined_2015")
  stationUserCombined2016 Int?    @map("station_user_combined_2016")
  stationUserCombined2017 Int?    @map("station_user_combined_2017")
  stationUserCombined2018 Int?    @map("station_user_combined_2018")
  stationUserCombined2019 Int?    @map("station_user_combined_2019")
  stationUserCombined2020 Int?    @map("station_user_combined_2020")
  stationUserCombined2021 Int?    @map("station_user_combined_2021")
  stationUserCombined2022 Int?    @map("station_user_combined_2022")
  stationUserCombined2023 Int?    @map("station_user_combined_2023")
  stationUserCombined2024 Int?    @map("station_user_combined_2024")
  stationUserCombined2025 Int?    @map("station_user_combined_2025")

  createdAt DateTime? @default(now()) @map("created_at")
  updatedAt DateTime? @updatedAt @map("updated_at")

  geoRailwayStations      GeoRailwayStation[]
  tllUserLambdaRecords    TllUserLambdaRecord[]
  valuationRecords        ValuationRecord[]
  proMansionRents         ProMansionRent[]
  systemReportViewHistory SystemReportViewHistory[]
  blogs                   Blog[]                    @relation("related_station_group")

  @@map("geo_railway_station_groups")
}

model GeoRailwayStation {
  id              String    @id @default(cuid())
  stationGroupId  String    @map("station_group_id")
  railwayLineId   String    @map("railway_line_id")
  name            String?   @db.VarChar(50)
  prefectureCode  Int       @map("prefecture_code")
  openDate        String?   @map("open_date") @db.VarChar(20)
  closeDate       String?   @map("close_date") @db.VarChar(20)
  stationUser     Json?     @map("station_user")
  stationUser2011 Int?      @map("station_user_2011")
  stationUser2012 Int?      @map("station_user_2012")
  stationUser2013 Int?      @map("station_user_2013")
  stationUser2014 Int?      @map("station_user_2014")
  stationUser2015 Int?      @map("station_user_2015")
  stationUser2016 Int?      @map("station_user_2016")
  stationUser2017 Int?      @map("station_user_2017")
  stationUser2018 Int?      @map("station_user_2018")
  stationUser2019 Int?      @map("station_user_2019")
  stationUser2020 Int?      @map("station_user_2020")
  stationUser2021 Int?      @map("station_user_2021")
  stationUser2022 Int?      @map("station_user_2022")
  stationUser2023 Int?      @map("station_user_2023")
  stationUser2024 Int?      @map("station_user_2024")
  stationUser2025 Int?      @map("station_user_2025")
  isOperating     Int       @map("is_operating")
  createdAt       DateTime? @default(now()) @map("created_at")
  updatedAt       DateTime? @updatedAt @map("updated_at")

  geoRailwayStationGroups GeoRailwayStationGroup? @relation(fields: [stationGroupId], references: [id])
  prefecture              GeoPrefecture?          @relation(fields: [prefectureCode], references: [code])

  @@index([prefectureCode, name])
  @@map("geo_railway_stations")
}

enum GeoRailwayStationRentsPropertyType {
  APARTMENT
  HOUSE
  MANSION
}

enum GeoRailwayStationRentsRoomLayoutType {
  ONE_R_ONE_K_ONE_DK       @map("1R/1K/1DK")
  ONE_LDK_TWO_K_TWO_DK     @map("1LDK/2K/2DK")
  TWO_LDK_THREE_K_THREE_DK @map("2LDK/3K/3DK")
  THREE_LDK_FOUR_K_FOUR_DK @map("3LDK/4K/4DK")
}

enum GeoRailwayStationRentsRoomSizeRange {
  TEN_TO_FIFTEEN        @map("10-15")
  FIFTEEN_TO_TWENTY     @map("15-20")
  TWENTY_TO_TWENTY_FIVE @map("20-25")
  TWENTY_FIVE_TO_THIRTY @map("25-30")
  THIRTY_TO_THIRTY_FIVE @map("30-35")
  THIRTY_FIVE_TO_FORTY  @map("35-40")
  FORTY_TO_FORTY_FIVE   @map("40-45")
  FORTY_FIVE_TO_FIFTY   @map("45-50")
  FIFTY_TO_SIXTY        @map("50-60")
  SIXTY_TO_SEVENTY      @map("60-70")
  SEVENTY_TO_EIGHTY     @map("70-80")
  EIGHTY_TO_NINETY      @map("80-90")
  NINETY_TO_ONE_HUNDRED @map("90-100")
}

// Start of Selection
enum GeoRailwayStationDistanceMinuteRange {
  ZERO_TO_FIVE      @map("0-5")
  FIVE_TO_TEN       @map("5-10")
  TEN_TO_FIFTEEN    @map("10-15")
  FIFTEEN_TO_TWENTY @map("15-20")
}

enum GeoRailwayStationRentsBuiltYearRange {
  ZERO_TO_ONE       @map("0-1")
  ZERO_TO_FIVE      @map("0-5")
  FIVE_TO_TEN       @map("5-10")
  TEN_TO_FIFTEEN    @map("10-15")
  FIFTEEN_TO_TWENTY @map("15-20")
  TWENTY_TO_THIRTY  @map("20-30")
}

model GeoRailwayStationRent {
  id                         String                                @id @default(cuid())
  stationName                String                                @map("station_name") @db.VarChar(255)
  railwayLineId              String?                               @map("railway_line_id")
  railwayLineName            String                                @map("railway_line_name") @db.VarChar(255)
  stationGroupId             String?                               @map("station_group_id")
  propertyType               GeoRailwayStationRentsPropertyType?   @map("property_type")
  roomLayoutType             GeoRailwayStationRentsRoomLayoutType? @map("room_layout_type")
  roomSizeRange              GeoRailwayStationRentsRoomSizeRange?  @map("room_size_range")
  stationDistanceMinuteRange GeoRailwayStationDistanceMinuteRange? @map("station_distance_minute_range")
  builtYearRange             GeoRailwayStationRentsBuiltYearRange? @map("built_year_range")
  price                      Float?
  createdAt                  DateTime?                             @default(now()) @map("created_at")
  updatedAt                  DateTime?                             @updatedAt @map("updated_at")

  @@map("geo_railway_station_rents")
}

model ProRawSumitomoAuction {
  id             String                         @id @default(cuid())
  sumitomoId     String?                        @map("sumitomo_id") @db.VarChar(255)
  lambdaRecordId String?                        @map("lambda_record_id")
  type           String?                        @map("type") @db.VarChar(255)
  recordType     TllUserLambdaRecordRecordType? @map("record_type")
  propertyStatus String?                        @map("property_status") @db.VarChar(128)

  name       String? @map("name") @db.VarChar(512)
  address    String? @map("address") @db.VarChar(512)
  buildingId String? @map("building_id") @db.VarChar(255)

  price        Int? @map("price")
  yearlyIncome Int? @map("yearly_income")

  longitude         Float?
  latitude          Float?
  landSize          Float?  @map("land_size")
  landRight         String? @map("land_right") @db.VarChar(128)
  buildingSize      Float?  @map("building_size")
  buildingMaterial  String? @map("building_material") @db.VarChar(128)
  buildingBuiltYear Int?    @map("building_built_year")

  nearestStation           String? @map("nearest_station") @db.VarChar(128)
  nearestStationWalkMinute Int?    @map("nearest_station_walk_minute")
  postalCode               String? @map("postal_code")

  infoStartDate DateTime? @map("info_start_date") @db.Timestamp(6)
  bidEndDate    DateTime? @map("bid_end_date") @db.Timestamp(6)
  pocName       String?   @map("poc_name") @db.VarChar(255)
  pocNumber     String?   @map("poc_number") @db.VarChar(255)
  pocEmail      String?   @map("poc_email") @db.VarChar(255)
  auctionUrl    String?   @map("auction_url") @db.VarChar(512)
  hpUrl         String?   @map("hp_url") @db.VarChar(512)

  materialLinks         Json?     @map("material_links")
  comments              String?   @db.Text
  commentsKeywordsCount Int       @default(0) @map("comments_keywords_count")
  isFav                 Int?      @map("is_fav")
  createdAt             DateTime? @default(now()) @map("created_at")
  updatedAt             DateTime? @updatedAt @map("updated_at")

  lambdaRecord TllUserLambdaRecord? @relation(fields: [lambdaRecordId], references: [id])
  building     ProBuilding?         @relation(fields: [buildingId], references: [id])

  @@index([bidEndDate, createdAt, isFav])
  @@index([address, pocName, type, id])
  @@index([commentsKeywordsCount, bidEndDate])
  @@map("pro_raw_sumitomo_auctions")
}

model TllUsersUserLambdaRecordsFavMap {
  id                 String    @id @default(cuid())
  userId             String?   @map("user_id")
  userLambdaRecordId String?   @map("user_lambda_record_id")
  createdAt          DateTime? @default(now()) @map("created_at")
  updatedAt          DateTime? @updatedAt @map("updated_at")

  user             TllUser?             @relation(fields: [userId], references: [id])
  userLambdaRecord TllUserLambdaRecord? @relation(fields: [userLambdaRecordId], references: [id])

  @@unique([userId, userLambdaRecordId])
  @@index([userId, userLambdaRecordId])
  @@index([userId, createdAt])
  @@map("tll_users_user_lambda_records_fav_maps")
}

enum AiSnsContentsContentSourceType {
  TITLE
  SUMMARY
  FULL_TEXT
  MANUAL
}

model AiSnsContent {
  id                                    String  @id @default(cuid())
  creatorUserId                         String  @map("creator_user_id")
  newsId                                String? @map("news_id")
  newsLink                              String? @map("news_link") @db.VarChar(500)
  newsTitle                             String? @map("news_title") @db.VarChar(500)
  newsDescription                       String? @map("news_description") @db.VarChar(500)
  newsLinkThumbnailUrl                  String? @map("news_link_thumbnail_url") @db.VarChar(500)
  newsLinkThumbnailUrlSupabase          String? @map("news_link_thumbnail_url_supabase") @db.VarChar(500)
  newsLinkThumbnailUrlSupabaseWithTitle String? @map("news_link_thumbnail_url_ls") @db.VarChar(500)
  newsSummary                           String? @map("news_summary") @db.Text
  newsFullTextManual                    String? @map("news_full_text_manual") @db.Text

  imageSourceType                     AiSnsContentsContentSourceType? @default(FULL_TEXT) @map("image_source_type")
  imagePrompt                         String?                         @map("image_prompt") @db.Text
  imagePromptRecommendedByOpenAi      String?                         @map("image_prompt_recommended_by_open_ai") @db.Text
  imagePromptRevisedByOpenAi          String?                         @map("image_prompt_revised_by_open_ai") @db.Text
  imageUrlByOpenAiOnSupabase          String?                         @map("image_url_by_open_ai_on_supabase") @db.Text
  imageUrlWithTitleByOpenAiOnSupabase String?                         @map("image_url_with_title_by_open_ai_on_supabase") @db.Text
  contentSourceType                   AiSnsContentsContentSourceType? @default(SUMMARY) @map("content_source_type")
  contentByOpenAiPrompt               String?                         @map("content_by_open_ai_prompt") @db.Text
  contentByOpenAi                     String?                         @map("content_by_open_ai") @db.Text
  contentByDeepSeekPrompt             String?                         @map("content_by_deep_seek_prompt") @db.Text
  contentByDeepSeek                   String?                         @map("content_by_deep_seek") @db.Text

  createdAt DateTime? @default(now()) @map("created_at")
  updatedAt DateTime? @updatedAt @map("updated_at")

  creator TllUser? @relation(fields: [creatorUserId], references: [id])
  news    AiNews?  @relation(fields: [newsId], references: [id])

  @@map("ai_sns_contents")
}

enum AiNewsSourceType {
  RAKUMACHI
  KENBIYA
  YAHOO_FUDOUSAN
  NIKKEI_FUDOUSAN
  TOYOKEIZAI_FUDOUSAN
}

model AiNews {
  id                   String           @id @default(cuid())
  source               AiNewsSourceType @map("source")
  releaseDate          DateTime         @map("release_date") @db.Date
  title                String?          @map("title") @db.VarChar(255)
  titleCh              String?          @map("title_ch") @db.VarChar(255)
  description          String?          @map("description") @db.Text
  descriptionCh        String?          @map("description_ch") @db.Text
  url                  String?          @unique @map("url") @db.VarChar(500)
  imageLink            String?          @map("image_link") @db.VarChar(500)
  readingCount         Int?             @map("reading_count")
  commentCount         Int?             @map("comment_count")
  recommendationLevel  Int?             @map("recommendation_level")
  recommendationReason String?          @map("recommendation_reason") @db.Text
  createdAt            DateTime         @default(now()) @map("created_at")
  updatedAt            DateTime         @updatedAt @map("updated_at")
  snsContents          AiSnsContent[]

  @@map("ai_news")
}

model AiSNSMetric {
  id                    String   @id @default(cuid())
  recordDate            String   @map("record_date")
  isoWeekNumber         Int      @map("iso_week_number")
  day                   Int      @map("day")
  month                 Int      @map("month")
  year                  Int      @map("year")
  xhsFollowers          Int?     @map("xhs_followers")
  wechatFollowers       Int?     @map("wechat_followers")
  wechatVideoFollowers  Int?     @map("wechat_video_followers")
  facebookFollowers     Int?     @map("facebook_followers")
  linkedinFollowers     Int?     @map("linkedin_followers")
  wechatGroupMembers    Int?     @map("wechat_group_members")
  whatsappGroupMembers  Int?     @map("whatsapp_group_members")
  wechatEnterpriseLeads Int?     @map("wechat_enterprise_leads")
  tiktokFollowers       Int?     @map("tiktok_followers")
  createdAt             DateTime @default(now()) @map("created_at")
  updatedAt             DateTime @updatedAt @map("updated_at")

  @@unique([recordDate])
  @@map("ai_sns_metrics")
}

// ALTER TABLE pro_buildings
// -- Add again with improved logic
// ALTER TABLE pro_buildings
// ADD COLUMN search_text tsvector GENERATED ALWAYS AS (
//   to_tsvector('simple',
//     regexp_replace(coalesce(name_ja, ''), '\s+', ' ', 'g')  -- normalize whitespace
//   )
// ) STORED;

// CREATE INDEX idx_pro_buildings_search_text ON pro_buildings USING GIN (search_text);

enum ProProjectType {
  CONSTRUCTION
  ADDITION
  DEMOLITION
}

model ProProject {
  id String @id @default(cuid())

  compositeTitle String         @map("composite_title") @db.Text
  name           String         @map("name_ja") @db.VarChar(255)
  address        String         @map("address") @db.VarChar(255)
  addressChiban  String?        @map("address_chiban") @db.VarChar(255)
  type           ProProjectType @map("project_type")

  postalCode     String? @map("postal_code")
  areaCode       Int?    @map("area_code")
  prefectureCode Int?    @map("prefecture_code")
  longitude      Float?
  latitude       Float?

  projectUsage     String? @map("project_usage") @db.VarChar(255)
  levelAboveGround Int?    @map("project_level_above_ground")
  levelBelowGround Int?    @map("project_level_below_ground")
  constructionArea Float?  @map("construction_area")

  projectStartDate    DateTime? @map("project_start_date") @db.Date
  projectEndDate      DateTime? @map("project_end_date") @db.Date
  projectEndDateYear  Int?      @map("project_end_date_year")
  projectEndDateMonth Int?      @map("project_end_date_month")

  recordCreatedAt DateTime @map("record_created_at")

  pcodes GeoPostalCode[]

  @@unique([compositeTitle]) // address + type + construction area + start date
  @@map("pro_projects")
}

model ProBuilding {
  id String @id @default(cuid())

  isKubun         Boolean @default(true) @map("is_kubun")
  nameJa          String? @map("name_ja") @db.VarChar(255)
  nameEn          String? @map("name_en") @db.VarChar(255)
  address         String  @map("address") @db.VarChar(255)
  addressDetailed String? @map("address_detailed") @db.VarChar(255)
  postalCode      String? @map("postal_code") @db.VarChar(50)

  longitude Float?
  latitude  Float?

  transport                String? @map("transport") @db.VarChar(255)
  nearestStation           String? @map("nearest_station") @db.VarChar(255)
  nearestStationGroupId    String? @map("nearest_station_group_id")
  nearestStationWalkMinute Int?    @map("nearest_station_walk_minute")

  level     Int?     @map("level")
  builtYear Int?     @map("built_year")
  link      String?  @map("link") @db.VarChar(255)
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  search_text Unsupported("tsvector")? @default(dbgenerated("to_tsvector('simple'::regconfig, regexp_replace((COALESCE(name_ja, ''::character varying))::text, '\\s+'::text, ' '::text, 'g'::text))"))

  tllUserLambdaRecords      TllUserLambdaRecord[]
  mansionRents              ProMansionRent[]
  systemReportViewHistories SystemReportViewHistory[]
  rawSumitomoAuctions       ProRawSumitomoAuction[]
  blogs                     Blog[]                    @relation("related_building")

  @@unique([nameJa, address])
  @@index([nameJa])
  @@map("pro_buildings")
}

enum ProRegistraionContinuationType {
  CONTINUATION_BEFORE // 連先
  CONTINUATION_AFTER // 連続
  SINGLE // 単独
}

enum ProRegistraionPropertyType {
  MANSION // 区建
  BUILDING_HOUSE // 建物
  LAND // 土地
}

model ProRegistrationRecord {
  id String @id @default(cuid())

  areaCode Int @map("area_code")
  localId  Int @map("local_id")

  registrationContinuationType ProRegistraionContinuationType @map("registration_continuation_type")
  registrationType             String?                        @map("registration_type") @db.VarChar(255)
  propertyIsNew                Boolean                        @map("property_is_new")
  propertyType                 ProRegistraionPropertyType?    @map("property_type")

  addressChiban String  @map("address_chiban") @db.VarChar(100)
  address       String? @map("address") @db.VarChar(100)
  postalCode    String? @map("postal_code")
  longitude     Float?  @map("longitude")
  latitude      Float?  @map("latitude")

  recordDate DateTime @map("record_date") @db.Timestamp(6)
  // area       GeoArea? @relation(fields: [areaCode], references: [code])

  @@unique([areaCode, localId])
  @@map("pro_registration_records")
}

enum ProBuildingHouseRentRecordType {
  BUILDING
  HOUSE
}

model ProBuildingHouseRent {
  id String @id @default(cuid())

  recordType   ProBuildingHouseRentRecordType @map("record_type")
  propertyType String?                        @map("property_type") @db.VarChar(100)

  brokerReinsNumber          String? @map("broker_reins_number") @db.VarChar(250)
  brokerType                 String? @map("broker_type") @db.VarChar(100)
  brokerStatus               String? @map("broker_status") @db.VarChar(100)
  brokerListingCompany       String? @map("broker_listing_company") @db.VarChar(500)
  brokerListingCompanyNumber String? @map("broker_listing_company_number") @db.VarChar(200)

  feeRent         Float?  @map("fee_rent")
  feeManagement   Float?  @map("fee_management")
  feeUtility      Float?  @map("fee_utility")
  feeGiftMoney    String? @map("fee_gift_money")
  feeDepositMoney String? @map("fee_deposit_money")

  landType          String? @map("land_type") @db.VarChar(100)
  landSize          Float?  @map("land_size")
  buildingBuiltYear Int?    @map("building_built_year")
  buildingSize      Float?  @map("building_size")
  buildingName      String? @map("building_name") @db.VarChar(500)
  buildingId        String? @map("building_id")
  buildingLayout    String? @map("building_layout") @db.VarChar(100)

  address                  String  @map("address") @db.VarChar(500)
  transport                String? @map("transport") @db.VarChar(100)
  nearestStation           String? @map("nearest_station") @db.VarChar(100)
  nearestStationGroupId    String? @map("nearest_station_group_id")
  nearestStationWalkMinute String? @map("nearest_station_walk_minute") @db.VarChar(100)

  roadConnection            String? @map("road_connection") @db.VarChar(100)
  roadConnectionFirstFacing String? @map("road_connection_first_facing") @db.VarChar(100)

  locationPostalCode String? @map("location_postal_code")

  locationLongitude      Float? @map("location_longitude")
  locationLatitude       Float? @map("location_latitude")
  locationPrefectureCode Int?   @map("location_prefecture_code")
  locationAreaCode       Int?   @map("location_area_code")

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("pro_building_house_rents")
}

enum ProMansionRentRecordType {
  MANSION
  BUILDING_PART
}

model ProMansionRent {
  id String @id @default(cuid())

  recordType   ProMansionRentRecordType? @map("record_type")
  propertyType String?                   @map("property_type") @db.VarChar(100)

  brokerReinsNumber          String? @map("broker_reins_number") @db.VarChar(250)
  brokerType                 String? @map("broker_type") @db.VarChar(100)
  brokerStatus               String? @map("broker_status") @db.VarChar(100)
  brokerListingCompany       String? @map("broker_listing_company") @db.VarChar(500)
  brokerListingCompanyNumber String? @map("broker_listing_company_number") @db.VarChar(200)
  feeRent                    Float?  @map("fee_rent")
  feeManagement              Float?  @map("fee_management")
  feeUtility                 Float?  @map("fee_utility")
  feeGiftMoney               String? @map("fee_gift_money")
  feeDepositMoney            String? @map("fee_deposit_money")

  landType          String? @map("land_type") @db.VarChar(100)
  unitSize          Float?  @map("unit_size")
  unitLayout        String? @map("unit_layout") @db.VarChar(100)
  unitLevel         Int?    @map("unit_level")
  buildingBuiltYear Int?    @map("building_built_year")
  buildingAddress   String  @map("building_address") @db.VarChar(500)
  buildingName      String? @map("building_name") @db.VarChar(500)
  buildingId        String? @map("building_id")

  transport                String? @map("transport") @db.VarChar(100)
  nearestStation           String? @map("nearest_station") @db.VarChar(100)
  nearestStationGroupId    String? @map("nearest_station_group_id")
  nearestStationWalkMinute String? @map("nearest_station_walk_minute") @db.VarChar(100)

  roadConnection            String? @map("road_connection") @db.VarChar(100)
  roadConnectionFirstFacing String? @map("road_connection_first_facing") @db.VarChar(100)

  locationLongitude      Float?  @map("location_longitude")
  locationLatitude       Float?  @map("location_latitude")
  locationPrefectureCode Int?    @map("location_prefecture_code")
  locationAreaCode       Int?    @map("location_area_code")
  locationPostalCode     String? @map("location_postal_code")

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  nearestStationGroup GeoRailwayStationGroup? @relation(fields: [nearestStationGroupId], references: [id])
  building            ProBuilding?            @relation(fields: [buildingId], references: [id])

  @@map("pro_mansion_rents")
}

model TllProperty {
  id Int @id

  recordId           String?   @map("record_id")
  title              String?   @map("title")
  buildingName       String?   @map("building_name") @db.VarChar(255)
  buildingNameEn     String?   @map("building_name_en") @db.VarChar(255)
  price              Int?      @map("price")
  saleIncomeMan      Int?      @map("sale_income_man")
  prevPrice          Int?      @map("prev_price")
  postalCode         String?   @map("postal_code")
  address            String?   @db.VarChar(255)
  addressEn          String?   @map("address_en") @db.VarChar(255)
  transport          String?   @map("transport") @db.VarChar(100)
  transportEn        String?   @map("transport_en") @db.VarChar(100)
  buildingType       String?   @map("building_type") @db.VarChar(30)
  material           String?   @map("material") @db.VarChar(20)
  rooms              Int
  level              Int
  yearBuilt          Int       @map("year_built")
  buildingSize       Float     @map("building_size")
  landSize           Float     @map("land_size")
  landCoverageRatio  Int       @map("land_coverage_ratio")
  landRight          String?   @map("land_right")
  landFloorAreaRatio Int       @map("land_floor_area_ratio")
  landUse            String?   @map("land_use")
  hasParking         Int?      @map("has_parking")
  imageUrls          String?   @map("image_urls")
  showHp             Int?      @map("show_hp")
  isSold             Int?      @map("is_sold")
  saleLayout         String?   @map("sale_layout") @db.VarChar(255)
  saleTags           String?   @map("sale_tags") @db.VarChar(255)
  saleStage          Int?      @map("sale_stage")
  saleContractDate   DateTime? @map("sale_contract_date") @db.Date
  salePurchaseDate   DateTime? @map("sale_purchase_date") @db.Date
  saleStartDate      DateTime? @map("sale_start_date") @db.Date
  saleEndDate        DateTime? @map("sale_end_date") @db.Date
  saleComments       String?   @map("sale_comments")
  createdAt          DateTime? @default(now()) @map("created_at")
  updatedAt          DateTime? @updatedAt @map("updated_at")

  userLambdaRecords TllUserLambdaRecord? @relation(fields: [recordId], references: [id])

  @@unique([recordId])
  @@map("tll_properties")
}

model TllReinsMetric {
  id String @id @default(cuid())

  reinsId        BigInt  @map("reins_id")
  listingTitle   String? @map("listing_title") @db.VarChar(255)
  listingType    String  @map("listing_type") @db.VarChar(255)
  listingSubtype String? @map("listing_subtype") @db.VarChar(255)

  address    String  @map("address") @db.VarChar(255)
  roomNumber String? @map("room_number") @db.VarChar(255)
  price      Int     @map("price")

  recordDate           DateTime  @default(now()) @map("record_date") @db.Date
  registrationDate     DateTime  @map("registration_date") @db.Date
  changeDate           DateTime? @map("change_date") @db.Date
  downloadDetailsCount Int       @map("download_details_count")
  checkDetailsCount    Int       @map("check_details_count")

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@unique([recordDate, reinsId])
  @@map("tll_reins_metrics")
}

model TrEvaluationCombinedRecord {
  id         String  @id @default(cuid())
  recordType String? @map("record_type") @db.VarChar(8)

  locationCode           String? @map("location_code")
  locationName           String? @map("location_name") @db.VarChar(250)
  nearestStation         String? @map("nearest_station") @db.VarChar(250)
  nearestStationDistance Int     @map("nearest_station_distance")
  addressRegistered      String? @map("address_registered")
  addressResidential     String? @map("address_residential")
  longitude              Float   @map("longitude")
  latitude               Float   @map("latitude")

  landArea                  Int?    @map("land_area")
  landUsage                 Int?    @map("land_usage")
  landBuildingCoverageRatio Int?    @map("land_building_coverage_ratio")
  landFloorAreaRatio        Int?    @map("land_floor_area_ratio")
  landOpeningRatio          Int?    @map("land_opening_ratio")
  landDepthRatio            Int?    @map("land_depth_ratio")
  landCityPlanningType      String? @map("land_city_planning_type") @db.VarChar(50)
  roadInfrontType           String? @map("road_infront_type") @db.VarChar(250)
  roadInfrontDirection      String? @map("road_infront_direction") @db.VarChar(50)
  roadInfrontWidth          Float?  @map("road_infront_width")

  buildingMaterial String? @map("building_material") @db.VarChar(20)
  levelAboveGround Int     @map("level_above_ground")
  levelUnderGround Int     @map("level_under_ground")

  kijun2012 Int? @map("kijun_2012")
  kijun2013 Int? @map("kijun_2013")
  kijun2014 Int? @map("kijun_2014")
  kijun2015 Int? @map("kijun_2015")
  kijun2016 Int? @map("kijun_2016")
  kijun2017 Int? @map("kijun_2017")
  kijun2018 Int? @map("kijun_2018")
  kijun2019 Int? @map("kijun_2019")
  kijun2020 Int? @map("kijun_2020")
  kijun2021 Int? @map("kijun_2021")
  kijun2022 Int? @map("kijun_2022")
  kijun2023 Int? @map("kijun_2023")
  kijun2024 Int? @map("kijun_2024")

  kouji2012 Int? @map("kouji_2012")
  kouji2013 Int? @map("kouji_2013")
  kouji2014 Int? @map("kouji_2014")
  kouji2015 Int? @map("kouji_2015")
  kouji2016 Int? @map("kouji_2016")
  kouji2017 Int? @map("kouji_2017")
  kouji2018 Int? @map("kouji_2018")
  kouji2019 Int? @map("kouji_2019")
  kouji2020 Int? @map("kouji_2020")
  kouji2021 Int? @map("kouji_2021")
  kouji2022 Int? @map("kouji_2022")
  kouji2023 Int? @map("kouji_2023")
  kouji2024 Int? @map("kouji_2024")

  createdAt DateTime? @default(now()) @map("created_at")
  updatedAt DateTime? @updatedAt @map("updated_at")

  @@map("tr_evaluation_combined_records")
}

model TrAuctionRecord {
  id                      String    @id @default(cuid())
  lambdaRecordId          String?   @map("lambda_record_id")
  status                  String?   @map("status") @db.VarChar(100)
  title                   String?   @map("title") @db.VarChar(255)
  bidPrice                Int?      @map("bid_price")
  valueRosenka            Int?      @map("value_rosenka")
  evaluationSekisan       Int?      @map("evaluation_sekisan")
  auctionType             String?   @map("auction_type") @db.VarChar(255)
  bidIdentifier           String?   @map("bid_identifier") @db.VarChar(255)
  bidAuthority            String?   @map("bid_authority") @db.VarChar(255)
  type                    String?   @map("type") @db.VarChar(255)
  subType                 String?   @map("sub_type") @db.VarChar(255)
  address                 String?   @map("address") @db.VarChar(255)
  access                  String?   @map("access") @db.VarChar(255)
  accessExtra             String?   @map("access_extra") @db.VarChar(255)
  nearestStation          String?   @map("nearest_station") @db.VarChar(255)
  nearestStationDistance  String?   @map("nearest_station_distance") @db.VarChar(255)
  nearestStationUserCount Int?      @map("nearest_station_user_count")
  builtDate               DateTime? @map("built_date") @db.Timestamp(6)
  buildingSize            Float?    @map("building_size")
  buildingMaterial        String?   @map("building_material") @db.VarChar(100)
  landSize                Float?    @map("land_size")
  biddingDetails          String?   @map("bidding_details")
  recordChanges           Json?     @map("record_changes")
  resultEstimate          Int?      @map("result_estimate")
  resultPrice             Int?      @map("result_price")
  resultType              String?   @map("result_type") @db.VarChar(24)
  resultBiddingCount      Int?      @map("result_bidding_count")
  resultWinnerType        String?   @map("result_winner_type") @db.VarChar(24)
  santenLink              String?   @map("santen_link") @db.VarChar(255)
  comments                String?   @map("comments")
  createdAt               DateTime? @default(now()) @map("created_at")
  updatedAt               DateTime? @updatedAt @map("updated_at")

  @@map("tr_auction_records")
}

model TrAuctionRecordChange {
  id                                String    @id @default(cuid())
  recordId                          String?   @map("record_id")
  recordDate                        String?   @map("record_date") @db.VarChar(32)
  bidPrice                          Int?      @map("bid_price")
  auctionType                       String?   @map("auction_type") @db.VarChar(255)
  auctionBidStartViewDate           DateTime? @map("auction_bid_start_view_date") @db.Date
  auctionBidStartDate               DateTime? @map("auction_bid_start_date") @db.Date
  auctionBidEndDate                 DateTime? @map("auction_bid_end_date") @db.Date
  auctionBidReleaseDate             DateTime? @map("auction_bid_release_date") @db.Date
  auctionBidDecideDate              DateTime? @map("auction_bid_decide_date") @db.Date
  auctionBidSpecialAuctionStartDate DateTime? @map("auction_bid_special_auction_start_date") @db.Date
  auctionBidSpecialAuctionEndDate   DateTime? @map("auction_bid_special_auction_end_date") @db.Date
  imageLink                         String?   @map("image_link") @db.VarChar(255)
  link                              String?   @db.VarChar(255)
  createdAt                         DateTime? @default(now()) @map("created_at")
  updatedAt                         DateTime? @updatedAt @map("updated_at")

  @@map("tr_auction_record_changes")
}

enum TrTransactionRecordType {
  FARM
  FOREST
  HOUSE
  LAND
  MANSION
}

model TrTransactionRecord {
  id String @id @default(cuid())

  postalCode     String? @map("postal_code")
  prefectureCode Int?    @map("prefecture_code")

  propertyType TrTransactionRecordType @map("property_type")
  landType     String?                 @map("land_type") @db.VarChar(50)

  municipalityCode Int?    @map("municipality_code")
  prefecture       String? @map("prefecture") @db.VarChar(50)
  cityWard         String? @map("city_ward") @db.VarChar(50)
  district         String? @map("district") @db.VarChar(50)

  nearestStation         String? @map("nearest_station") @db.VarChar(50)
  nearestStationGroupId  Int?    @map("nearest_station_group_id")
  nearestStationDistance Int?    @map("nearest_station_distance")

  transactionPrice               Int     @map("transaction_price")
  transactionPricePerSquareMeter Int?    @map("transaction_price_per_square_meter")
  layout                         String? @map("layout") @db.VarChar(20)
  landSize                       Float?  @map("land_size")
  landShape                      String? @map("land_shape") @db.VarChar(50)
  frontage                       Float?  @map("frontage")
  houseSize                      Float?  @map("house_size")
  builtYear                      String? @map("built_year") @db.VarChar(20)
  material                       String? @map("material") @db.VarChar(50)
  buildingUse                    String? @map("building_use") @db.VarChar(50)
  frontRoadDirection             String? @map("front_road_direction") @db.VarChar(20)
  frontRoadClassification        String? @map("front_road_classification") @db.VarChar(20)
  frontRoadBreadth               Float?  @map("front_road_breadth")
  areaCityPlanningType           String? @map("area_city_planning_type") @db.VarChar(200)
  landBuildingLandRatio          Int?    @map("land_building_land_ratio")
  landFloorAreaRatio             Int?    @map("land_floor_area_ratio")

  reformed Int?    @map("reformed")
  comments String? @map("comments") @db.VarChar(255)

  transactionQuartileStartDate DateTime  @map("transaction_quartile_start_date") @db.Timestamp(6)
  createdAt                    DateTime? @default(now()) @map("created_at")
  updatedAt                    DateTime? @updatedAt @map("updated_at")

  @@map("tr_transaction_records")
}

// Start of Selection
model PropertyMaterialNameMapping {
  id           String  @id @map("id") @db.VarChar(255) // 修改为不使用默认名称
  name         String  @map("name") @db.VarChar(255)
  uploadUserId String? @map("upload_user_id") @db.VarChar(255)
  recordId     String  @map("record_id") @db.VarChar(255)

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  tllUserLambdaRecord TllUserLambdaRecord @relation(fields: [recordId], references: [id])
  tllUser             TllUser?            @relation(fields: [uploadUserId], references: [id])

  @@index([recordId])
  @@map("property_material_name_mappings")
}

enum PropertyWatchedCriteriaType {
  POSTAL_CODE
  STATION_GROUP_ID
  BUILDING_ID
}

model PropertyWatchedCriteria {
  id String @id @default(cuid())

  userId          String                        @map("user_id") @db.VarChar(255)
  watchRecordType TllUserLambdaRecordRecordType @default(BUILDING) @map("watch_record_type")
  watchType       PropertyWatchedCriteriaType   @map("watch_type")
  watchValue      String                        @map("watch_value") @db.VarChar(255)

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  tllUser TllUser @relation(fields: [userId], references: [id])

  @@map("property_watched_criteria")
  @@ignore
}

model PropertyAnalysisResult {
  id                 String                        @id @default(cuid())
  recordId           String?                       @map("record_id") @db.VarChar(255)
  valuationRecordId  String?                       @map("valuation_record_id") @db.VarChar(255)
  recordType         TllUserLambdaRecordRecordType @map("record_type")
  recordYearlyIncome Float?                        @map("record_yearly_income")
  valuationCoef      Float                         @default(1) @map("valuation_coef")

  nearbyRecordIdAndDistance  Json?  @map("nearby_record_id_and_distance")
  nearbyAverageRent          Float? @map("nearby_average_rent")
  nearbyMinCap               Float? @map("nearby_min_cap")
  nearbyMaxCap               Float? @map("nearby_max_cap")
  nearbyAvgCap               Float? @map("nearby_avg_cap")
  nearbyLinearRegressionA    Float? @map("nearby_linear_regression_a")
  nearbyLinearRegressionB    Float? @map("nearby_linear_regression_b")
  nearby80PCap               Float? @map("nearby_80p_cap")
  nearbyMinGfa               Float? @map("nearby_min_gfa")
  nearbyMaxGfa               Float? @map("nearby_max_gfa")
  nearbyAvgGfa               Float? @map("nearby_avg_gfa")
  nearby80PGfa               Float? @map("nearby_80p_gfa")
  nearbyMinIssuePrice        Float? @map("nearby_min_issue_price")
  nearbyMaxIssuePrice        Float? @map("nearby_max_issue_price")
  nearbyAvgIssuePrice        Float? @map("nearby_avg_issue_price")
  nearby80PIssuePrice        Float? @map("nearby_80p_issue_price")
  nearbyMinRent              Float? @map("nearby_min_rent")
  nearbyMaxRent              Float? @map("nearby_max_rent")
  nearbyAvgRent              Float? @map("nearby_avg_rent")
  nearby80PRent              Float? @map("nearby_80p_rent")
  nearbyMinSekisanPercentage Float? @map("nearby_min_sekisan_percentage")
  nearbyMaxSekisanPercentage Float? @map("nearby_max_sekisan_percentage")
  nearbyAvgSekisanPercentage Float? @map("nearby_avg_sekisan_percentage")
  nearby80PSekisanPercentage Float? @map("nearby_80p_sekisan_percentage")

  roiValuation           Json? @map("roi_valuation")
  rentValuation          Json? @map("rent_valuation")
  gfaValuation           Json? @map("gfa_valuation")
  sekisanValuation       Json? @map("sekisan_valuation")
  unitLandPriceValuation Json? @map("unit_land_price_valuation")
  overallStarLevel       Int?  @map("overall_star_level")

  analysisDate DateTime @default(now()) @map("analysis_date") @db.Date
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  tllUserLambdaRecord TllUserLambdaRecord? @relation(fields: [recordId], references: [id])
  valuationRecord     ValuationRecord?     @relation(fields: [valuationRecordId], references: [id])

  @@unique([valuationRecordId])
  @@unique([recordId])
  @@map("property_analysis_results")
}

enum SystemMetricKeyEnum {
  TOTAL_PROPERTY_BUILDING_RECORD_COUNT
  TOTAL_PROPERTY_HOUSE_RECORD_COUNT
  TOTAL_PROPERTY_MANSION_RECORD_COUNT
  TOTAL_PROPERTY_LAND_RECORD_COUNT

  TOTAL_PROPERTY_CHANGE_RECORD_COUNT

  TOTAL_PROPERTY_BUILDING_RENT_RECORD_COUNT
  TOTAL_PROPERTY_HOUSE_RENT_RECORD_COUNT
  TOTAL_PROPERTY_MANSION_RENT_RECORD_COUNT

  TOTAL_VALUATION_RECORD_COUNT

  TOTAL_USER_COUNT
  TOTAL_USER_PAID_COUNT

  TOTAL_USER_DAU_COUNT
  TOTAL_USER_WAU_COUNT
  TOTAL_USER_MAU_COUNT

  TOTAL_MRR
}

model SystemMetric {
  id String @id @default(cuid())

  key   SystemMetricKeyEnum @map("key")
  value String              @map("value") @db.VarChar(255)

  recordDate DateTime @map("record_date") @db.Date
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")

  @@unique([recordDate, key])
  @@map("system_metric")
}

enum SystemUserActivityEventTypeEnum {
  PAGE_VIEW
  BUTTON_CLICK
  FORM_SUBMIT
  LOGIN
  LOGOUT
}

model SystemUserActivity {
  id     String  @id @default(cuid())
  userId String? @map("user_id") @db.VarChar(255)

  route           String                          @map("route") @db.Text
  routeNormalized String?                         @map("route_normalized") @db.Text
  eventType       SystemUserActivityEventTypeEnum @map("event_type")
  eventMetadata   Json?                           @map("event_metadata")
  userAgent       String?                         @map("user_agent") @db.Text
  referer         String?                         @map("referer") @db.Text

  recordDate DateTime @default(now()) @map("record_date") @db.Date
  createdAt  DateTime @default(now()) @map("created_at")

  user TllUser? @relation(fields: [userId], references: [id])

  @@map("system_user_activities")
}

enum SystemReportViewHistoryRecordType {
  MANSION
  POSTAL_CODE
  STATION_GROUP
}

model SystemReportViewHistory {
  id String @id @default(cuid())

  userId     String                            @map("user_id")
  recordType SystemReportViewHistoryRecordType @map("record_type")

  buildingId            String? @map("building_id")
  postalCode            String? @map("postal_code")
  nearestStationGroupId String? @map("nearest_station_group_id")

  viewDate DateTime @map("view_date") @db.Timestamp(6)
  isValid  Boolean  @default(true) @map("is_valid")

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  building            ProBuilding?            @relation(fields: [buildingId], references: [id])
  nearestStationGroup GeoRailwayStationGroup? @relation(fields: [nearestStationGroupId], references: [id])
  user                TllUser                 @relation(fields: [userId], references: [id])
  GeoPostalCode       GeoPostalCode?          @relation(fields: [geoPostalCodeId], references: [id])
  geoPostalCodeId     String?

  @@index([userId, buildingId, isValid])
  @@map("system_report_view_histories")
}

model ReferralCode {
  id String @id @default(cuid()) // 保留系统唯一标识

  code            String @unique // 对外展示的邀请码
  createdByUserId String

  createdAt DateTime @default(now())
  expiresAt DateTime
  isActive  Boolean  @default(true)

  referredUsers TllUser[] @relation("referral_code_users")
  createdByUser TllUser   @relation("referral_code_created_by_user", fields: [createdByUserId], references: [id])

  @@map("referral_codes")
}

model Blog {
  id String @id @default(cuid())

  creatorUserId String? @map("creator_user_id")
  viewZh        Int     @default(0) @map("view_zh")
  viewEn        Int     @default(0) @map("view_en")
  viewJa        Int     @default(0) @map("view_ja")

  buildingId     String? @map("building_id")
  stationGroupId String? @map("station_group_id")
  postalCodeId   String? @map("postal_code_id")
  areaCode       Int?    @map("area_code")

  creatorUser TllUser? @relation(fields: [creatorUserId], references: [id])

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  building     ProBuilding?            @relation("related_building", fields: [buildingId], references: [id])
  stationGroup GeoRailwayStationGroup? @relation("related_station_group", fields: [stationGroupId], references: [id])
  postalCode   GeoPostalCode?          @relation("related_postal_codes", fields: [postalCodeId], references: [id])
  area         GeoArea?                @relation("related_area", fields: [areaCode], references: [code])

  @@map("blogs")
}
