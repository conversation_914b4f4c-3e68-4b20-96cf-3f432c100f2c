"use client";
import React, { useEffect, useState } from "react";
import Link from "next/link"; // Linkコンポーネントをインポート
import { Button } from "@/components/ui/button"; // Shadcn UIのButtonコンポーネントをインポート
import Image from "next/image"; // Imageコンポーネントをインポート
import { useAuthStore } from "@/store/auth"; // 导入 auth 状态管理
import {
  BarChart,
  Calculator,
  LayoutDashboard,
  LogIn,
  MessageCircle,
  Search,
  UserPlus,
} from "lucide-react"; // 导入仪表盘图标
import { BadgeJapaneseYen } from "lucide-react";
import { LandPlot } from "lucide-react";
import LanguageSwitcher from "./languageSwitcher"; // 导入 LanguageSwitcher 组件
import { usePathname } from "next/navigation"; // 导入 usePathname
import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Menu } from "lucide-react";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuIndicator,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  NavigationMenuViewport,
} from "@/components/ui/navigation-menu";

import { navigationMenuTriggerStyle } from "@/components/ui/navigation-menu";

export default function Header({
  isInBackendLayout = false,
  isShowingMidMenu = true,
}: {
  isInBackendLayout?: boolean;
  isShowingMidMenu?: boolean;
}) {
  const { setCurrentUser, currentUser } = useAuthStore(); // 获取设置用户的函数
  const [isScrolled, setIsScrolled] = useState(false); // 新增状态以跟踪滚动
  const router = useRouter();
  const [isHydrated, setIsHydrated] = useState(false);
  const t = useTranslations();
  async function fetchCurrentUser() {
    const res = await fetch("/api/auth");
    if (res.ok) {
      const user = await res.json();
      setCurrentUser(user);
    }
  }

  useEffect(() => {
    if (!currentUser) {
      fetchCurrentUser();
    }
  }, []); // 依赖于 setCurrentUser

  useEffect(() => {
    setIsHydrated(true);
  }, []);

  const pathname = usePathname(); // 获取当前路径

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > window.innerHeight); // 检查滚动位置
    };

    window.addEventListener("scroll", handleScroll); // 监听滚动事件
    return () => {
      window.removeEventListener("scroll", handleScroll); // 清理事件监听器
    };
  }, []);

  const isBlogDetailPage = pathname.match(/^\/blog\/[a-z]{2}\/.+/);

  const featureLinks = [
    {
      title: t("Header.menuFeatureSearch"),
      description: t("Header.menuFeatureSearchDetails"),
      url: "/feature/search",
      icon: Search,
    },
    {
      title: t("Header.menuFeatureRent"),
      description: t("Header.menuFeatureRentDetails"),
      url: "/feature/rent",
      icon: BadgeJapaneseYen,
    },
    {
      title: t("Header.menuFeatureValuation"),
      description: t("Header.menuFeatureValuationDetails"),
      url: "/feature/valuation",
      icon: BarChart,
    },
    {
      title: t("Header.menuFeatureMarketInsight"),
      description: t("Header.menuFeatureMarketInsightDetails"),
      url: "/feature/insight",
      icon: LandPlot,
    },
  ];

  const blogLinks = [
    {
      type: t("Header.menuResourceBlog"),
      items: [
        {
          title: t("Header.menuResourceBlogMarket"),
          href: "/blog?category=d335dc6d-c44a-4dde-ab0e-4b238af8a2ce",
          description: t("Header.menuResourceBlogMarketDetails"),
        },
        {
          title: t("Header.menuResourceBlogSpot"),
          href: "/blog?category=66ef8dc7-15c2-49bb-ad7a-addf170ea819",
          description: t("Header.menuResourceBlogSpotDetails"),
        },
        {
          title: t("Header.menuResourceBlogBukken"),
          href: "/blog?category=5967e932-eb75-412f-840f-cdedd90fc15f",
          description: t("Header.menuResourceBlogBukkenDetails"),
        },
        {
          title: t("Header.menuResourceBlogUrbalytics"),
          href: "/blog?category=13fc624f-addd-4c6b-b8e4-ec14c7c72a6a",
          description: t("Header.menuResourceBlogUrbalyticsDetails"),
        },
      ],
    },
    {
      type: t("Header.menuResourceFreeTool"),
      items: [
        {
          title: t("Header.menuResourceFreeToolCF"),
          href: "/it/cf",
          description: t("Header.menuResourceFreeToolCFDetails"),
        },
        {
          title: t("Header.menuResourceFreeToolChiban"),
          href: "/it/chiban",
          description: t("Header.menuResourceFreeToolChibanDetails"),
        },
        {
          title: t("Header.menuResourceFreeToolCapRate"),
          href: "/it/caprate",
          description: t("Header.menuResourceFreeToolCapRateDetails"),
        },
        {
          title: t("Header.menuResourceGlossary"),
          href: "/glossary",
          description: t("Header.menuResourceGlossaryDetails"),
        },
      ],
    },
  ];

  return (
    <header
      className={`fixed ${pathname === "/" ? (isScrolled ? "bg-white border-b border-neutral-200" : "bg-transparent") : "bg-white border-b border-neutral-200"} top-0 left-0 right-0 z-50 text-white`}
      style={{ height: "var(--header-height)" }}
    >
      {" "}
      {/* CSS変数を使用して高さを設定 */}
      <div
        className={`w-full ${isInBackendLayout ? "" : "max-w-[1200px]"} mx-auto px-4 flex justify-between items-center h-full`}
      >
        <Link href="/" className="flex items-center">
          <Image
            src="/icon-192x192.png"
            alt="Urbalyticsロゴ"
            width={32}
            height={32}
          />{" "}
          {/* アイコンを追加 */}
          <span
            className={`text-lg ${isScrolled ? "text-black" : pathname === "/" ? "text-white" : "text-black"} ml-2`}
          >
            Urbalytics
          </span>
        </Link>

        {isShowingMidMenu && (
          <div
            className={`hidden md:flex items-center ${isScrolled ? "bg-transparent text-black" : pathname === "/" ? "text-white" : "text-black"} justify-center`}
          >
            <NavigationMenu>
              <NavigationMenuList>
                <NavigationMenuItem>
                  <NavigationMenuTrigger className="text-sm bg-transparent">
                    {t("Header.menuFeature")}
                  </NavigationMenuTrigger>
                  <NavigationMenuContent>
                    <ul className="grid w-[360px] p-4">
                      {featureLinks.map((link) => (
                        <NavigationMenuLink
                          asChild
                          key={link.title}
                          className="hover:bg-neutral-100 p-4 rounded-md"
                        >
                          <Link
                            href={link.url}
                            className="flex flex-row items-center"
                          >
                            {link.icon && (
                              <link.icon className="mr-4 h-8 w-8 text-neutral-500" />
                            )}

                            <div className="flex flex-col">
                              <div className="font-medium flex items-center">
                                {" "}
                                {link.title}
                              </div>
                              <div className="text-muted-foreground text-sm">
                                {link.description}
                              </div>
                            </div>
                          </Link>
                        </NavigationMenuLink>
                      ))}
                    </ul>
                  </NavigationMenuContent>
                </NavigationMenuItem>

                <NavigationMenuItem>
                  <NavigationMenuTrigger className="text-sm bg-transparent">
                    {t("Header.menuResource")}
                  </NavigationMenuTrigger>
                  <NavigationMenuContent>
                    <ul className="grid w-[400px] gap-2 md:w-[500px] md:grid-cols-2 lg:w-[600px] p-4">
                      {blogLinks.map((link, index) => (
                        <div className="flex flex-col gap-2" key={index}>
                          <div className="text-lg leading-none font-bold p-4 border-b border-neutral-200 tracking-wider">
                            {link.type}
                          </div>
                          {link.items.map((item) => (
                            <NavigationMenuLink
                              asChild
                              key={item.title}
                              className="hover:bg-neutral-100 p-4 rounded-md"
                            >
                              <Link
                                href={item.href}
                                className="flex flex-row items-center"
                              >
                                <div className="flex flex-col gap-1">
                                  <div className="font-medium flex items-center">
                                    {" "}
                                    {item.title}
                                  </div>
                                  <div className="text-muted-foreground text-sm">
                                    {item.description}
                                  </div>
                                </div>
                              </Link>
                            </NavigationMenuLink>
                          ))}
                        </div>
                      ))}
                    </ul>
                  </NavigationMenuContent>
                </NavigationMenuItem>
              </NavigationMenuList>
            </NavigationMenu>

            {/* <Button variant="ghost" className="px-4 py-2 rounded-md" asChild>
            <Link href="/#sales-point">
              {t("Header.menuFeature")}
            </Link>
          </Button> */}

            <Button variant="ghost" className="px-4 py-2 rounded-md" asChild>
              <Link href="/solution">{t("Header.menuSolution")}</Link>
            </Button>

            <Button variant="ghost" className="px-4 py-2 rounded-md" asChild>
              <Link href="/pricing">{t("Header.menuPricing")}</Link>
            </Button>

            {/* <Button variant="ghost" className="px-4 py-2 rounded-md" asChild>
            <Link href="/blog">
              {t("Header.menuBlog")}
            </Link>
          </Button> */}

            <Button variant="ghost" className="px-4 py-2 rounded-md" asChild>
              <Link href="/contact-us">{t("Header.menuContactUs")}</Link>
            </Button>
          </div>
        )}

        <div className="flex space-x-2">
          {" "}
          {/* 間隔を追加 */}
          {currentUser ? ( // 如果用户已登录，显示仪表盘按钮
            <>
              <Link href="/ex/search" className="w-8 hidden md:flex">
                <Button
                  variant="ghost"
                  className={`px-2 flex items-center ${isScrolled ? "text-black" : pathname === "/" ? "text-white" : "text-black"}`}
                >
                  <LayoutDashboard /> {/* 添加仪表盘图标 */}
                </Button>
              </Link>
            </>
          ) : (
            // 仅在当前用户为 null 时显示登录按钮
            <div className="hidden md:flex space-x-2">
              <Button
                variant="outline"
                className={`${isScrolled ? "bg-black text-white" : pathname === "/" ? "bg-transparent text-white" : "text-black"}`}
                onClick={() => {
                  router.push("/login?tab=signUp");
                }}
              >
                {t("Header.signUpForFree")}
              </Button>

              <Button
                variant="ghost"
                className={`${isScrolled ? "text-black" : pathname === "/" ? "text-white" : "text-black"}`}
                onClick={() => {
                  router.push("/login");
                }}
              >
                {t("Header.login")}
              </Button>
            </div>
          )}
          {!isBlogDetailPage && (
            <LanguageSwitcher
              color={
                isScrolled
                  ? "text-black"
                  : pathname === "/"
                    ? "text-white"
                    : "text-black"
              }
            />
          )}
          <div className="md:hidden">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className={`px-2 flex items-center ${isScrolled ? "text-black" : pathname === "/" ? "text-white" : "text-black"}`}
                >
                  <Menu />
                </Button>
              </DropdownMenuTrigger>

              <DropdownMenuContent align="end" className="w-56">
                {currentUser ? (
                  <>
                    <DropdownMenuItem asChild>
                      <Link href="/ex/search" className="flex items-center">
                        <LayoutDashboard className="mr-2 h-4 w-4" />
                        <span>{t("Menu.searchMain")}</span>
                      </Link>
                    </DropdownMenuItem>

                    <DropdownMenuItem asChild>
                      <Link
                        href="/ex/search/valuation"
                        className="flex items-center"
                      >
                        <Calculator className="mr-2 h-4 w-4" />
                        <span>{t("Menu.searchValuation")}</span>
                      </Link>
                    </DropdownMenuItem>
                    {/* <DropdownMenuSeparator /> */}
                  </>
                ) : (
                  <>
                    <DropdownMenuItem asChild>
                      <Link href="/login" className="flex items-center">
                        <LogIn className="mr-2 h-4 w-4" />
                        <span>{t("Header.login")}</span>
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link
                        href="/login?tab=signUp"
                        className="flex items-center"
                      >
                        <UserPlus className="mr-2 h-4 w-4" />
                        <span>{t("Header.signUpForFree")}</span>
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />

                    <DropdownMenuItem asChild>
                      <Link href="/#sales-point">
                        {t("Header.menuFeature")}
                      </Link>
                    </DropdownMenuItem>

                    <DropdownMenuItem asChild>
                      <Link href="/pricing">{t("Header.menuPricing")}</Link>
                    </DropdownMenuItem>

                    <DropdownMenuItem asChild>
                      <Link href="/blog">{t("Header.menuBlog")}</Link>
                    </DropdownMenuItem>

                    <DropdownMenuItem asChild>
                      <Button
                        variant="ghost"
                        className="px-4 pl-2 rounded-md"
                        onClick={() => {
                          window.open("mailto:<EMAIL>", "_blank");
                        }}
                      >
                        {t("Header.menuContactUs")}
                      </Button>
                    </DropdownMenuItem>
                  </>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
    </header>
  );
}
