"use client";

import { Separator } from "./ui/separator";
import Link from "next/link";
import dynamic from "next/dynamic";
import Script from "next/script";
import LanguageSwitcher from "./languageSwitcher";
import { useTranslations } from "next-intl";
import { useAuthStore } from "@/store/auth";
import {
  ExternalLink,
  FacebookIcon,
  Linkedin,
  Mail,
  MapPin,
  Phone,
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { MessageCircle } from "lucide-react";
import Image from "next/image";

export default function FooterForNonLogin({
  isInBackendLayout = false,
  hideLanguageSwitcher = false,
}: {
  isInBackendLayout?: boolean;
  hideLanguageSwitcher?: boolean;
}) {
  const headerT = useTranslations("Header");
  const t = useTranslations("Footer");
  const currentUser = useAuthStore((state) => state.currentUser);

  let menuItems = {
    feature: [
      {
        name: headerT("menuFeatureSearch"),
        link: "/feature/search",
      },
      {
        name: headerT("menuFeatureRent"),
        link: "/feature/rent",
      },
      {
        name: headerT("menuFeatureValuation"),
        link: "/feature/valuation",
      },
      {
        name: headerT("menuFeatureMarketInsight"),
        link: "/feature/insight",
      },
    ],
    solution: [
      {
        name: t("solutionKaitori"),
        link: "/solution",
      },
      {
        name: t("solutionChuukai"),
        link: "/solution",
      },
      {
        name: t("solutionChintai"),
        link: "/solution",
      },
      {
        name: t("solutionShisankanri"),
        link: "/solution",
      },
      {
        name: t("solutionMinpaku"),
        link: "/solution",
      },
      {
        name: t("solutionKinyuu"),
        link: "/solution",
      },
    ],
    resource: [
      {
        name: t("resourceBlogMarket"),
        link: "/blog?category=d335dc6d-c44a-4dde-ab0e-4b238af8a2ce",
      },
      {
        name: t("resourceBlogSpot"),
        link: "/blog?category=66ef8dc7-15c2-49bb-ad7a-addf170ea819",
      },
      {
        name: t("resourceBlogBukken"),
        link: "/blog?category=5967e932-eb75-412f-840f-cdedd90fc15f",
      },
      {
        name: t("resourceBlogUrbalytics"),
        link: "/blog?category=13fc624f-addd-4c6b-b8e4-ec14c7c72a6a",
      },
      {
        name: t("resourceCF"),
        link: "/it/cf",
      },
      {
        name: t("resourceMinpaku"),
        link: "/it/minpaku",
      },
      {
        name: t("resourceChiban"),
        link: "/it/chiban",
      },
      {
        name: t("resourceCapRate"),
        link: "/it/caprate",
      },
      {
        name: t("resourceGlossary"),
        link: "/glossary",
      },
    ],
    support: [
      {
        name: t("supportBookDemo"),
        link: "/contact-us",
      },
      {
        name: t("supportStart"),
        link: "/login?type=signUp",
      },
      // {
      //   name: t('supportCommunity'),
      //   link: "/resource3"
      // },
      // {
      //   name: t('supportContact'),
      //   link: "/resource4"
      // },
      {
        name: t("supportApp"),
        link: "/app",
      },
    ],
    company: [
      {
        name: t("companyAbout"),
        link: "/about-urbalytics",
      },
      {
        name: t("companyPricing"),
        link: "/pricing",
      },
      // {
      //   name: t('companyEvents'),
      //   link: "/company3"
      // },
      {
        name: (
          <div className="flex flex-row gap-1 items-center justify-start">
            {t("companyOperatingCompany")}
            <ExternalLink className="w-4 h-4" />
          </div>
        ),
        link: "https://www.tll.jp",
      },
      {
        name: (
          <div className="flex flex-row gap-1 items-center justify-start">
            {t("companyCareers")}
            <ExternalLink className="w-4 h-4" />
          </div>
        ),
        link: "https://jp.indeed.com/cmp/Tll%E5%90%88%E5%90%8C%E4%BC%9A%E7%A4%BE/jobs",
      },
      {
        name: t("companySignIn"),
        link: "/login?type=login",
      },
      {
        name: t("companySignUp"),
        link: "/login?type=signUp",
      },
    ],
  };

  const renderSocialMedia = ({
    link,
    action,
  }: {
    link: React.ReactNode;
    action: string;
  }) => {
    return (
      <Dialog>
        <DialogTrigger asChild>{link}</DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              <div className="text-lg font-bold">{action}</div>
              <div className="text-sm text-neutral-500">
                {action}するには、下記のQRコードをスキャンしてください。
              </div>
            </DialogTitle>
            <DialogDescription className="flex flex-col gap-4 text-center items-center p-4">
              <div className="flex flex-row gap-4">
                <div className="flex flex-col gap-2">
                  <Image
                    src="/assets/wecom-qr.jpg"
                    alt="Wechat"
                    width={200}
                    height={200}
                  />

                  {/* 添加下载图片的按钮 */}
                  <Button
                    variant="outline"
                    className="w-full text-xs"
                    onClick={() => {
                      const link = document.createElement("a");
                      link.href = "/assets/wecom-qr.jpg"; // 图片的路径
                      link.download = "urbalytics-support-wecom-qr.jpg"; // 下载时的文件名
                      document.body.appendChild(link);
                      link.click();
                      document.body.removeChild(link);
                    }}
                  >
                    WeChat QRコードを
                    <br />
                    ダウンロード
                  </Button>
                </div>

                <div className="flex flex-col gap-2">
                  <Image
                    src="/assets/whatsapp-qr.jpg"
                    alt="Wechat"
                    width={200}
                    height={200}
                  />

                  <div className="flex flex-col gap-2 rounded-md">
                    <Button
                      variant="outline"
                      className="w-full text-xs"
                      onClick={() => {
                        const link = document.createElement("a");
                        link.href = "/assets/whatsapp-qr.jpg"; // 图片的路径
                        link.download = "urbalytics-support-whatsapp-qr.jpg"; // 下载时的文件名
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                      }}
                    >
                      WhatsApp QRコードを
                      <br />
                      ダウンロード
                    </Button>
                  </div>
                </div>
              </div>
            </DialogDescription>
          </DialogHeader>
        </DialogContent>
      </Dialog>
    );
  };

  return (
    <footer className="pb-8 px-4 border-t py-4 sm:py-16  border-neutral-200 w-full text-center sm:text-left  text-neutral-900 height-[var(--footer-height)] flex flex-col items-start gap-2">
      <div
        className={`w-full ${isInBackendLayout ? "" : "max-w-[1200px]"} mx-auto`}
      >
        <div className="grid grid-cols-1 sm:grid-cols-5 gap-8 sm:gap-2 text-left pb-8">
          {Object.entries(menuItems).map(([key, value]) => (
            <div className="flex flex-col gap-2" key={key}>
              <div className="text-lg font-bold mb-2">{t(`${key}`)}</div>
              {value.map((item, index) => (
                <Link
                  href={item.link}
                  key={index}
                  className="text-sm text-neutral-900 hover:underline"
                >
                  {item.name}
                </Link>
              ))}

              {key === "support" &&
                renderSocialMedia({
                  link: (
                    <div className="text-sm text-neutral-900 hover:underline">
                      {t("supportCommunity")}
                    </div>
                  ),
                  action: "コミュニティに参加",
                })}
            </div>
          ))}
        </div>

        <Separator className="mt-4 mb-4 border-nereutral-100" />

        <div className="text-sm text-neutral-500 flex flex-col sm:flex-row gap-2 justify-start items-start sm:items-center">
          <div className="flex flex-row gap-2 items-center">
            <Mail className="w-6 h-6" />
            <Link
              href="mailto:<EMAIL>"
              className="text-neutral-900 text-sm hover:underline"
            >
              <EMAIL>
            </Link>
          </div>

          <Separator orientation="vertical" className="h-4 hidden sm:block" />

          <div className="flex flex-row gap-2 items-center">
            <MapPin className="w-6 h-6" />
            <Link
              href="https://maps.google.com/?q=東京都渋谷区神泉町11-11イーデンビル3F"
              target="_blank"
              rel="noopener noreferrer"
              className="text-neutral-900 text-sm hover:underline"
            >
              {t("address")}
            </Link>
          </div>
        </div>

        <Separator className="mt-4 mb-4 border-neutral-100" />

        <div className="flex flex-col sm:flex-row gap-2 justify-center items-center w-full">
          <div className="flex flex-col flex-1 gap-2 justify-start items-start">
            <div className="text-sm flex flex-col sm:flex-row gap-2 justify-start items-center w-full">
              <div className="flex flex-row gap-2 justify-center items-center">
                <Linkedin
                  className="bg-neutral-800 text-white w-6 h-6 p-0.5 cursor-pointer rounded-md"
                  onClick={() => {
                    window.open(
                      "https://jp.linkedin.com/company/urbalytics",
                      "_blank",
                    );
                  }}
                />
                <FacebookIcon
                  className="bg-neutral-800 text-white w-6 h-6 p-0.5 cursor-pointer rounded-md"
                  onClick={() => {
                    window.open(
                      "https://www.facebook.com/urbalytics",
                      "_blank",
                    );
                  }}
                />

                <Image
                  src="/icons/xiaohongshu.svg"
                  className="cursor-pointer rounded-md"
                  alt="Urbalytics"
                  width={25}
                  height={25}
                  onClick={() => {
                    window.open(
                      "https://www.xiaohongshu.com/user/profile/585731be5e87e74511f79c14?channel_type=web_note_detail_r10&parent_page_channel_type=web_profile_board&xsec_token=ABuX_GXtI1Pt6xldqH5D26M8f-lThdOkEFst2ShY9PC7I=&xsec_source=pc_note&wechatWid=82673e84f30cd33cc722fd40dc2c25fa&wechatOrigin=menu",
                      "_blank",
                    );
                  }}
                />

                {renderSocialMedia({
                  link: (
                    <Image
                      src="/icons/weixin.svg"
                      className="cursor-pointer rounded-md"
                      alt="Urbalytics"
                      width={24}
                      height={24}
                    />
                  ),
                  action: "WeChat/Whatsapp担当を追加",
                })}

                <Link href="/tos" className="text-neutral-900">
                  {t("tos")}
                </Link>
                <Separator orientation="vertical" className="h-4" />
                <Link href="/pp" className="text-neutral-900">
                  {t("pp")}
                </Link>
                <Separator
                  orientation="vertical"
                  className="h-4 hidden sm:block"
                />
                <Link href="/cd" className="text-neutral-900">
                  {t("cd")}
                </Link>
              </div>

              <div className="text-sm text-neutral-400 flex flex-row gap-2">
                ©2025 Urbalytics
                <div>
                  <Link href="https://tll.jp" className="underline">
                    TLL LLC
                  </Link>
                  {t("runBy")}
                </div>
                {/* <div className="flex justify-center text-xs gap-2">
        <span>
          ビルド: {process.env.COMMIT_HASH}
        </span>
        <Separator orientation="vertical" className="h-4" />
        <span>
          ビルド日: {process.env.COMMIT_DATE}
        </span>
      </div> */}
              </div>
            </div>

            <div className="text-xs text-neutral-500 w-full flex flex-col gap-2  border-neutral-200">
              <div>{t("disclosure")}</div>
            </div>
          </div>

          {!hideLanguageSwitcher && (
            <div className="border border-neutral-400 rounded-md">
              <LanguageSwitcher color={"text-black"} />
            </div>
          )}
        </div>
      </div>
    </footer>
  );
}
