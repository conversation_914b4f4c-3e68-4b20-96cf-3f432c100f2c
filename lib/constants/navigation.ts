import Layout from "@/app/(cp)/layout";
import {
  Search,
  BarChart, // 新增图标
  MapPin, // 新增图标
  Heart, // 更合适的图标替换 Star
  Users,
  LayoutDashboard,
  Facebook,
  TicketCheck,
  LucideIcon,
  ScanEye,
  FileText,
  FileClock,
  HousePlus,
  Twitter,
  JapaneseYen,
  Building2,
  TrainFront,
  LandPlot,
  ContactRound,
  PencilRuler,
  SearchCode,
  House,
  CopyPlusIcon,
  HandshakeIcon,
  Rss,
  Construction,
  BookOpen,
  ScrollText,
  Banknote,
  BadgeJapaneseYen,
  Home,
  Percent,
} from "lucide-react";
import { useTranslations } from "next-intl";

export const SIDE_BAR_MAIN_DATA_RAW = [
  {
    title: "search",
    url: "/ex",
    menus: [
      {
        title: "searchMain",
        url: "/ex/search",
        icon: Search,
        items: [
          {
            title: "searchHistory",
            url: "/ex/search/history",
            // icon: FileClock,
          },
        ],
      },
      {
        title: "searchValuation",
        url: "/ex/valuation",
        icon: <PERSON><PERSON>hart, // 修改为更合适的图标
        items: [
          {
            title: "searchValuationHistory",
            url: "/ex/valuation/history",
          },
        ],
      },
      {
        title: "rentValuation",
        url: "/ex/rent",
        icon: BadgeJapaneseYen, // 使用更合适的图标
      },
      // {
      //   title: "analysisArea",
      //   url: "/an/area",
      //   icon: LandPlot, // 使用更合适的图标
      // },
      {
        title: "searchFav",
        url: "/ex/fav",
        icon: Heart, // 修改为更合适的图标
      },
      {
        title: "toolWatch",
        url: "/ex/need",
        icon: ScanEye,
      },
      // {
      //   title: "競売物件",
      //   url: "/ex/keibaiNormal",
      //   icon: HandCoins, // 修改为更合适的图标
      //   disable: true,
      // },
    ],
    accessLevel: 1,
  },
  {
    title: "tool",
    url: "/it",
    menus: [
      {
        title: "kensetsu",
        url: "/it/insight",
        icon: Construction,
      },
      {
        title: "cf",
        url: "/it/cf",
        icon: Banknote,
      },
      {
        title: "minpaku",
        url: "/it/minpaku",
        icon: Home,
      },
      {
        title: "chiban",
        url: "/it/chiban",
        icon: MapPin,
      },
      {
        title: "caprate",
        url: "/it/caprate",
        icon: Percent,
      },
      {
        title: "uketsuke",
        url: "/it/uketsuke",
        icon: ScrollText,
        accessLevel: 20,
      },
      // {
      //   title: "物件CF分析",
      //   url: "/it/cf",
      //   icon: Coins,
      //   disable: true,
      // },
      // {
      //   title: "ローン診断",
      //   url: "/it/loan",
      //   icon: Landmark,
      //   disable: true,
      // },
    ],
    accessLevel: 1,
  },
  {
    title: "tllAgent",
    url: "/pm",
    menus: [
      {
        title: "tllAgentCustomer",
        url: "/pa/customer",
        icon: Users, // 使用更合适的图标
      },
      {
        title: "tllAgentReferralCode",
        url: "/pa/referralCode",
        icon: HandshakeIcon, // 使用更合适的图标
      },
      {
        title: "adminSNS",
        url: "/pa/post",
        icon: Twitter,
      },
      // {
      //   title: "tllAgentProposal",
      //   url: "/pa/proposal",
      //   icon: FileText, // 使用更合适的图标
      //   disable: true,
      // }
    ],
    accessLevel: 30,
  },
  {
    title: "admin",
    url: "/ad",
    menus: [
      {
        title: "adminNew",
        url: "/ad/dashboard",
        icon: HousePlus, // 使用更合适的图标
      },
      {
        title: "adminRent",
        url: "/ad/rent",
        icon: BadgeJapaneseYen, // 使用更合适的图标
      },
      {
        title: "adminKeibaiSumifu",
        url: "/ad/keibaiSumifu",
        icon: MapPin, // 修改为更合适的图标
        items: [
          {
            title: "adminKeibaiSumifuWakeari",
            url: "/ad/keibaiSumifu/wakeari",
          },
        ],
      },
      {
        title: "adminBid",
        url: "/ad/bid",
        icon: TicketCheck, // 使用更合适的图标
        items: [
          {
            title: "adminBidMetrics",
            url: "/ad/bid/metrics",
          },
          {
            title: "adminBidChecklist",
            url: "/ad/bid/checklist",
          },
        ],
      },
      {
        title: "adminSell",
        url: "/ad/sell",
        icon: JapaneseYen, // 使用更合适的图标
      },
      {
        title: "adminSNSContent",
        url: "/ad/sns",
        icon: Facebook, // 使用更合适的图标
        items: [
          {
            title: "adminSNSMetrics",
            url: "/ad/sns/metrics",
          },
          {
            title: "adminSNSHotspot",
            url: "/ad/sns/hotspot",
          },
          {
            title: "adminSNSCreate",
            url: "/ad/sns/create",
          },
        ],
      },
      {
        title: "adminSNSBlog",
        url: "/ad/blog",
        icon: Rss,
      },
      {
        title: "analysisCompany",
        url: "/an/company",
        icon: ContactRound, // 使用更合适的图标
        accessLevel: 90,
        items: [
          {
            title: "analysisCompanyFav",
            url: "/an/company/fav",
            icon: Heart, // 修改为更合适的图标
            disable: true,
          },
        ],
      },
    ],
    accessLevel: 90,
  },
  {
    title: "superAdmin",
    url: "/su",
    menus: [
      {
        title: "superAdminDashboard",
        url: "/su/dashboard",
        icon: LayoutDashboard, // 使用更合适的图标
        items: [
          {
            title: "superAdminDashboardUserActivity",
            url: "/su/dashboard/userActivity",
          },
        ],
      },
      {
        title: "superAdminData",
        url: "/su/data",
        icon: PencilRuler, // 使用更合适的图标
        items: [
          {
            title: "superAdminBuilding",
            url: "/su/data/building",
          },
          {
            title: "superAdminOwnBuilding",
            url: "/su/data/tllBuilding",
          },
          {
            title: "superAdminMerge",
            url: "/su/data/merge",
          },
        ],
      },
      {
        title: "superAdminUser",
        url: "/su/users",
        icon: Users, // 使用更合适的图标
      },
    ],
    accessLevel: 99,
  },
] as {
  title: string;
  url: string;
  accessLevel?: number;
  menus: {
    title: string;
    url: string;
    icon: LucideIcon;
    accessLevel?: number;
    disable?: boolean;
    items?: {
      title: string;
      url: string;
      supposedAccessLevel?: number;
      accessLevel?: number;
    }[];
  }[];
}[];
