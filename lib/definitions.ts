// This file contains type definitions for your data.
// It describes the shape of the data, and what data type each property should accept.
// For simplicity of teaching, we're manually defining these types.
// However, these types are generated automatically if you're using an ORM such as Prisma.

import { any, z } from "zod";
import { ProBuildingSchema } from "./definitions/proBuilding";
import { TllUserFormSchema } from "./definitions/tllUser";
import {
  UserLambdaRecordSchema,
  UserLambdaRecordType,
} from "./definitions/userLambdaRecord";

import { ProCompanySchema } from "./definitions/proCompany";
export type ActionResponse<T = null> = {
  success: boolean;
  data?: any;
  message?: string;
  errors?: Record<string, string[]>; // Field-specific errors
};

export enum TllCustomerMainNeedType {
  RENTAL = "RENTAL",
  INVESTMENT_BUILDING = "INVESTMENT_BUILDING",
  INVESTMENT_UNIT = "INVESTMENT_UNIT",
  INVESTMENT_HOUSE = "INVESTMENT_HOUSE",
  INVESTMENT_LAND = "INVESTMENT_LAND",
  OWNER_OCCUPIED_UNIT = "OWNER_OCCUPIED_UNIT",
  OWNER_OCCUPIED_HOUSE = "OWNER_OCCUPIED_HOUSE",
  OWNER_OCCUPIED_LAND = "OWNER_OCCUPIED_LAND",
}

export enum TllCustomerSource {
  URBALYTICS = "URBALYTICS",
  TLL_REFERAL = "TLL_REFERAL",
  AGENT_REFERAL = "AGENT_REFERAL",
  RAKUMACHI = "RAKUMACHI",
  KENBIYA = "KENBIYA",
  ATHOME_SUUMO = "ATHOME_SUUMO",
  XIAOHONGSHU = "XIAOHONGSHU",
  WECHAT_GROUP = "WECHAT_GROUP",
}

export enum TllCustomerNeedCriteriaType {
  POSTAL_CODE = "POSTAL_CODE",
  STATION_GROUP_ID = "STATION_GROUP_ID",
  BUILDING_ID = "BUILDING_ID",
  AREA_CODE = "AREA_CODE",
}

export const CustomerNeedSchema = z.object({
  id: z.string().optional(),
  customerUserId: z.string().optional(),
  agentUserId: z.string().optional(),

  title: z.string().optional(),
  priority: z.number().optional(),
  recordType: z.nativeEnum(UserLambdaRecordType).optional(),
  priceFrom: z.number().optional(),
  priceTo: z.number().optional(),
  roiFrom: z.number().optional(),

  criteriaType: z.nativeEnum(TllCustomerNeedCriteriaType).optional(),
  postalCodes: z.string().optional(),
  nearestStationGroupIds: z.string().optional(),
  areaCodes: z.string().optional(),
  nearestStationWalkMinuteTo: z.number().optional(),
  buildingIds: z.string().optional(),

  landAreaFrom: z.number().optional(),
  landAreaTo: z.number().optional(),
  buildingAreaFrom: z.number().optional(),
  buildingAreaTo: z.number().optional(),
  buildingMaterial: z.string().optional(),
  yearFrom: z.number().optional(),

  isFollow: z.number().optional(),
  landCanHotel: z.number().optional(),
  comment: z.string().optional(),
  customer: z.any().optional(),
});
export type CustomerNeedProps = z.infer<typeof CustomerNeedSchema>;

export const CustomerFormSchema = z.object({
  id: z.string().optional(),
  name: z
    .string()
    .min(1, {
      message: "名前は2文字以上でなければなりません。",
    })
    .optional(),
  agentUserId: z.string().nullable().optional(),
  language: z.string().optional(),
  email: z.union([z.string().email(), z.string().length(0)]).optional(), // ✅ Allow empty string or valid email, which is the default
  phone: z.string().optional(),
  address: z.string().optional(),
  age: z.number().optional(),
  income: z.number().optional(),
  jobVisa: z.string().optional(),
  comments: z.string().optional(),

  mainNeedType: z
    .nativeEnum(TllCustomerMainNeedType)
    .default(TllCustomerMainNeedType.INVESTMENT_BUILDING),
  source: z
    .nativeEnum(TllCustomerSource)
    .default(TllCustomerSource.TLL_REFERAL),

  needs: z.array(CustomerNeedSchema).optional(),
  agentUser: z.any().optional(),
});
export type CustomerProps = z.infer<typeof CustomerFormSchema>;

export const CustomerFollowUpRecordSchema = z.object({
  id: z.string().optional(),
  agentUserId: z.string(),
  customerId: z.string(),
  followUpDate: z.date(),
  comments: z.string().optional(),
  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
  customer: z.any().optional(),
  agentUser: z.any().optional(),
});
export type CustomerFollowUpRecordProps = z.infer<
  typeof CustomerFollowUpRecordSchema
>;

export const TllUserPushSubscriptionSchema = z.object({
  id: z.string(),
  userId: z.string(),
  deviceId: z.string(),
  subscription: z.any(),
  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
});
export type TllUserPushSubscriptionProps = z.infer<
  typeof TllUserPushSubscriptionSchema
>;

export const UserLambdaRecordCreateSchema = z
  .object({
    recordType: z.nativeEnum(UserLambdaRecordType),
    price: z.number(),
    yearlyIncome: z.number().optional(),
    address: z.string(),
    landSize: z.number(),
    landRight: z.string(),
    buildingSize: z.number().optional(),
    buildingMaterial: z.string().optional(),
    buildingBuiltYear: z.number().optional(),
    nearestStation: z.any().optional(), // this is from the ui dropdown
    nearestStationWalkMinute: z.number().optional(),
  })
  .superRefine((data, ctx) => {
    if (
      data.recordType === UserLambdaRecordType.BUILDING ||
      data.recordType === UserLambdaRecordType.HOUSE
    ) {
      if (
        data.buildingSize === undefined ||
        data.buildingSize === null ||
        data.buildingSize === 0
      ) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Building size is required for building records",
          path: ["buildingSize"],
        });
      }

      if (
        data.buildingMaterial === undefined ||
        data.buildingMaterial === null ||
        data.buildingMaterial === ""
      ) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Building material is required for building records",
          path: ["buildingMaterial"],
        });
      }
    }
  });
export type UserLambdaRecordCreateProps = z.infer<
  typeof UserLambdaRecordCreateSchema
>;

export const UserLambdaRecordCreateLandSchema = z.object({
  recordType: z.nativeEnum(UserLambdaRecordType),
  price: z.number(),
  address: z.string(),
  landSize: z.number(),
  landRight: z.string(),
  nearestStation: z.string(),
  nearestStationWalkMinute: z.number(),
});
export type UserLambdaRecordCreateLandProps = z.infer<
  typeof UserLambdaRecordCreateLandSchema
>;

export const UserLambdaRecordCreateBuildingHouseSchema = z.object({
  recordType: z.nativeEnum(UserLambdaRecordType),
  price: z.number(),
  yearlyIncome: z.number().optional(),
  address: z.string(),
  landSize: z.number(),
  landRight: z.string(),
  buildingSize: z.number(),
  buildingMaterial: z.string(),
  buildingBuiltYear: z.number(),
  nearestStation: z.string(),
  nearestStationWalkMinute: z.number(),
});
export type UserLambdaRecordCreateBuildingProps = z.infer<
  typeof UserLambdaRecordCreateBuildingHouseSchema
>;

export enum TllUserLambdaRecordPriceChangeSourceType {
  REINS = "REINS",
  SUUMO = "SUUMO",
  RAKUMACHI = "RAKUMACHI",
  SITE_NOMU = "SITE_NOMU",
  SITE_SUMITOMO = "SITE_SUMITOMO",
  SITE_LIVABLE = "SITE_LIVABLE",
  SITE_MITSUI = "SITE_MITSUI",
  SITE_MITSUBISHI = "SITE_MITSUBISHI",
  KEIBAI_SUMITOMO = "KEIBAI_SUMITOMO",
}

export const ValuationRecordSchema = z.object({
  id: z.string().optional(),
  userId: z.string().optional(),
  recordType: z.nativeEnum(UserLambdaRecordType),
  compositeTitle: z.string().optional(), // 修改为可选的 String?
  price: z.number(), // 修改为可选的 Int?
  yearlyIncome: z.number().optional(), // 修改为可选的 Float?
  transport: z.string().optional(), // 修改为可选的 String?
  nearestStation: z.string().optional(), // 修改为可选的 String?
  nearestStationGroupId: z.string().optional(), // 修改为可选的 Int?
  nearestStationWalkMinute: z.number().optional(), // 修改为可选的 Int?
  valueRosenka: z.number().optional(), // 修改为可选的 Int?

  prefectureCode: z.number().optional(), // 修改为可选的 Int?
  areaCode: z.number().optional(), // 修改为可选的 Int?
  postalCode: z.string().optional(), // 修改为可选的 Int?
  address: z.string().optional(), // 修改为可选的 String?
  longitude: z.number().optional(), // 修改为可选的 Float?
  latitude: z.number().optional(), // 修改为可选的 Float?

  landSize: z.number().optional(), // 修改为可选的 Float?
  landRight: z.string().optional(), // 修改为可选的 String?
  buildingSize: z.number().optional(), // 修改为可选的 Float?
  buildingName: z.string().optional(), // 修改为可选的 String?
  buildingMaterial: z.string().optional(), // 修改为可选的 String?
  buildingLayout: z.string().optional(), // 修改为可选的 String?
  buildingBuiltYear: z.number().optional(), // 修改为可选的 Int?
  buildingLevel: z.string().optional(), // 修改为可选的 String?
  buildingRoomCount: z.number().optional(), // 修改为可选的 String?
  landType: z.string().optional(), // 修改为可选的 String?
  landBuildingCoverageRatio: z.number().optional(), // 修改为可选的 Int?
  landFloorAreaRatio: z.number().optional(), // 修改为可选的 Int?
  roadConnection: z.string().optional(), // 修改为可选的 String?
  roadConnectionFirstType: z.string().optional(), // 修改为可选的 String?
  roadConnectionFirstFacing: z.string().optional(), // 修改为可选的 String?
  roadConnectionFirstWidth: z.string().optional(), // 修改为可选的 String?
  roadConnectionSecondType: z.string().optional(), // 修改为可选的 String?
  roadConnectionSecondFacing: z.string().optional(), // 修改为可选的 String?
  roadConnectionSecondWidth: z.string().optional(), // 修改为可选的 String?

  analysisSimulationConfig: z.any().optional(), // 修改为可选的 Json?
  analysisSimulationResults: z.any().optional(), // 修改为可选的 Json?
  salesComments: z.string().optional(), // 修改为可选的 String?

  valuationDate: z.date().optional(), // 修改为可选的 DateTime?
  createdAt: z.date().optional(), // 修改为可选的 DateTime?
  updatedAt: z.date().optional(), // 修改为可选的 DateTime?

  user: TllUserFormSchema.optional(),
  nearestStationGroup: z.any().optional(), // 修改为可选的 Json?
});
export type ValuationRecordProps = z.infer<typeof ValuationRecordSchema>;

export const TllUserLambdaRecordSearchHistorySchema = z.object({
  id: z.string(),
  userId: z.string(),
  recordId: z.string(),
  searchDate: z.date(),
  isValid: z.boolean(),
  createdAt: z.date(),
  updatedAt: z.date(),

  tllUserLambdaRecord: UserLambdaRecordSchema,
});
export type TllUserLambdaRecordSearchHistoryProps = z.infer<
  typeof TllUserLambdaRecordSearchHistorySchema
>;

export const RailwayStationGroupSchema = z.object({
  id: z.string(),
  name: z.string(),
  prefectureCode: z.number(), // 修改为可选的 Int?
  postalCode: z.string().optional(), // 修改为可选的 Int?
  address: z.string().optional(), // 修改为可选的 Int?
  longitude: z.number(), // 修改为可选的 Int?
  latitude: z.number(), // 修改为可选的 Int?
  stationUserCombined2011: z.number().optional(), // 修改为可选的 Int?
  stationUserCombined2012: z.number().optional(), // 修改为可选的 Int?
  stationUserCombined2013: z.number().optional(), // 修改为可选的 Int?
  stationUserCombined2014: z.number().optional(), // 修改为可选的 Int?
  stationUserCombined2015: z.number().optional(), // 修改为可选的 Int?
  stationUserCombined2016: z.number().optional(), // 修改为可选的 Int?
  stationUserCombined2017: z.number().optional(), // 修改为可选的 Int?
  stationUserCombined2018: z.number().optional(), // 修改为可选的 Int?
  stationUserCombined2019: z.number().optional(), // 修改为可选的 Int?
  stationUserCombined2020: z.number().optional(), // 修改为可选的 Int?
  stationUserCombined2021: z.number().optional(), // 修改为可选的 Int?
  stationUserCombined2022: z.number().optional(), // 修改为可选的 Int?
  stationUserCombined2023: z.number().optional(), // 修改为可选的 Int?
  stationUserCombined2024: z.number().optional(), // 修改为可选的 Int?
  stationUserCombined2025: z.number().optional(), // 修改为可选的 Int?
});
export type RailwayStationGroupProps = z.infer<
  typeof RailwayStationGroupSchema
>;

export const RailwayStationSchema = z.object({
  id: z.string(),
  name: z.string(),
  prefectureCode: z.number().optional(), // 修改为可选的 Int?
  areaCode: z.number().optional(), // 修改为可选的 Int?

  prefecture: z.any().optional(), // 修改为可选的 Int?
  geoRailwayStationGroups: RailwayStationGroupSchema,
});
export type RailwayStationProps = z.infer<typeof RailwayStationSchema>;

export const SumitomoAuctionSchema = z.object({
  id: z.string().optional(),
  sumitomoId: z.string().optional(),
  lambdaRecordId: z.string().optional(),

  type: z.string().optional(),
  recordType: z.nativeEnum(UserLambdaRecordType).optional(),

  name: z.string().optional(),
  address: z.string().optional(),
  price: z.number().optional(),
  yearlyIncome: z.number().optional(),
  landSize: z.number().optional(),
  landRight: z.string().optional(),
  buildingSize: z.number().optional(),
  buildingMaterial: z.string().optional(),
  buildingBuiltYear: z.number().optional(),
  nearestStation: z.string().optional(),
  nearestStationWalkMinute: z.number().optional(),
  longitude: z.number().optional(),
  latitude: z.number().optional(),
  postalCode: z.string().optional(),
  propertyStatus: z.string().optional(),
  auctionUrl: z.string().optional(),
  hpUrl: z.string().optional(),
  infoStartDate: z.date().optional(),
  bidEndDate: z.date().optional(),
  pocName: z.string().optional(),
  pocNumber: z.string().optional(),
  pocEmail: z.string().optional(),
  materialLinks: z.any().optional(),
  comments: z.string().optional(),
  commentsKeywordsCount: z.number().optional(),
  isFav: z.number().optional(),
  pastMatches: z.array(z.any()).optional(),
  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),

  lambdaRecord: UserLambdaRecordSchema.optional(),
  building: ProBuildingSchema.optional(),
});
export type SumitomoAuctionProps = z.infer<typeof SumitomoAuctionSchema>;

export const GeoPrefectureSchema = z.object({
  code: z.string(),
  name: z.string(),
  longitude: z.number().optional(),
  latitude: z.number().optional(),
  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
});
export type GeoPrefectureProps = z.infer<typeof GeoPrefectureSchema>;

export const GeoCitySchema = z.object({
  prefectureCode: z.number(),
  name: z.string().optional(),
  nameKatakana: z.string().optional(),
  longitude: z.number().optional(),
  latitude: z.number().optional(),
  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
});
export type GeoCityProps = z.infer<typeof GeoCitySchema>;

export const GeoAddressSchema = z.object({
  id: z.string(),
  pcode: z.string().optional(),
  address: z.string().optional(),

  chibanAddress: z.string().optional(),
  chibanDetails: z.string().optional(),

  longitude: z.number().optional(),
  latitude: z.number().optional(),

  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
});
export type GeoAddressProps = z.infer<typeof GeoAddressSchema>;

export const GeoAreaSchema = z.object({
  id: z.string(),
  code: z.string().optional(),
  nameJa: z.string().optional(),
  nameKatakana: z.string().optional(),
  longitude: z.number().optional(),
  latitude: z.number().optional(),
  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
  prefecture: GeoPrefectureSchema.optional(),
  city: GeoCitySchema.optional(),
});
export type GeoAreaProps = z.infer<typeof GeoAreaSchema>;

export const GeoPostalCodeSchema = z.object({
  id: z.string(),
  postalCode: z.string().optional(),
  prefectureCode: z.number(),
  prefectureName: z.string().optional(),
  cityCode: z.string().optional(),
  cityName: z.string().optional(),
  areaCode: z.number().optional(),
  areaName: z.string().optional(),
  choumeName: z.string().optional(),
  buildingAveragePrice: z.number().optional(),
  buildingRecordCount: z.number().optional(),
  buildingRecordRoi: z.number().optional(),
  houseRecordCount: z.number().optional(),
  houseAveragePrice: z.number().optional(),
  landRecordCount: z.number().optional(),
  landAveragePrice: z.number().optional(),
  mansionRecordCount: z.number().optional(),
  mansionAveragePrice: z.number().optional(),
  longitude: z.number().optional(),
  latitude: z.number().optional(),
  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
});

export type GeoPostalCodeProps = z.infer<typeof GeoPostalCodeSchema>;

export const AiSnsMetricsSchema = z.object({
  id: z.string().optional(),
  recordDate: z.string(),
  isoWeekNumber: z.number().optional(),
  day: z.number().optional(),
  month: z.number().optional(),
  year: z.number().optional(),
  xhsFollowers: z.number().optional(),
  wechatFollowers: z.number().optional(),
  wechatVideoFollowers: z.number().optional(),
  facebookFollowers: z.number().optional(),
  linkedinFollowers: z.number().optional(),
  wechatGroupMembers: z.number().optional(),
  whatsappGroupMembers: z.number().optional(),
  wechatEnterpriseLeads: z.number().optional(),
  tiktokFollowers: z.number().optional(),

  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
});

export type AiSnsMetricsProps = z.infer<typeof AiSnsMetricsSchema>;

export enum AiSnsContentsContentSourceType {
  TITLE = "TITLE",
  SUMMARY = "SUMMARY",
  FULL_TEXT = "FULL_TEXT",
  MANUAL = "MANUAL",
}

export const AiSnsContentSchema = z.object({
  id: z.string().optional(),
  creatorUserId: z.string().optional(),
  newsId: z.string().optional(),
  newsLink: z.string().optional(),
  newsTitle: z.string().optional(),
  newsDescription: z.string().optional(),
  newsLinkThumbnailUrl: z.string().optional(),
  newsLinkThumbnailUrlSupabase: z.string().optional(),
  newsLinkThumbnailUrlSupabaseWithTitle: z.string().optional(),
  newsSummary: z.string().optional(),
  newsFullTextManual: z.string().optional(),
  imageSourceType: z.nativeEnum(AiSnsContentsContentSourceType).optional(),
  imagePrompt: z.string().optional(),
  imagePromptRecommendedByOpenAi: z.string().optional(),
  imagePromptRevisedByOpenAi: z.string().optional(),
  imageUrlByOpenAiOnSupabase: z.string().optional(),
  imageUrlWithTitleByOpenAiOnSupabase: z.string().optional(),
  contentSourceType: z.nativeEnum(AiSnsContentsContentSourceType).optional(),
  contentByOpenAiPrompt: z.string().optional(),
  contentByOpenAi: z.string().optional(),
  contentByDeepSeekPrompt: z.string().optional(),
  contentByDeepSeek: z.string().optional(),

  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
});

export type AiSnsContentProps = z.infer<typeof AiSnsContentSchema>;

export enum AiNewsSourceType {
  RAKUMACHI = "RAKUMACHI",
  KENBIYA = "KENBIYA",
  YAHOO_FUDOUSAN = "YAHOO_FUDOUSAN",
  NIKKEI_FUDOUSAN = "NIKKEI_FUDOUSAN",
  TOYOKEIZAI_FUDOUSAN = "TOYOKEIZAI_FUDOUSAN",
}

export const AiNewsSchema = z.object({
  id: z.string().optional(),
  source: z.nativeEnum(AiNewsSourceType),
  releaseDate: z.date(),
  url: z.string().optional(),
  title: z.string().optional(),
  titleCh: z.string().optional(),
  description: z.string().optional(),
  descriptionCh: z.string().optional(),
  imageLink: z.string().optional(),
  readingCount: z.number().optional(),
  commentCount: z.number().optional(),
  recommendationLevel: z.number().optional(),
  recommendationReason: z.string().optional(),
  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
});

export type AiNewsProps = z.infer<typeof AiNewsSchema>;

export const TrEvaluationCombinedRecordsSchema = z.object({
  id: z.string(),
  recordType: z.string().optional(),
  locationCode: z.string(),
  locationName: z.string().optional(),
  landUsage: z.number().optional(),
  addressRegistered: z.string().optional(),
  addressResidential: z.string().optional(),
  longitude: z.number().optional(),
  latitude: z.number().optional(),
  landArea: z.number().optional(),
  landBuildingCoverageRatio: z.number().optional(),
  landFloorAreaRatio: z.number().optional(),
  landOpeningRatio: z.number().optional(),
  landDepthRatio: z.number().optional(),
  landCityPlanningType: z.string().optional(),
  nearestStation: z.string().optional(),
  nearestStationDistance: z.number(),
  buildingMaterial: z.string().optional(),
  levelAboveGround: z.number(),
  levelUnderGround: z.number(),
  roadInfrontType: z.string().optional(),
  roadInfrontDirection: z.string().optional(),
  roadInfrontWidth: z.number().optional(),
  kijun2012: z.number().optional(),
  kijun2013: z.number().optional(),
  kijun2014: z.number().optional(),
  kijun2015: z.number().optional(),
  kijun2016: z.number().optional(),
  kijun2017: z.number().optional(),
  kijun2018: z.number().optional(),
  kijun2019: z.number().optional(),
  kijun2020: z.number().optional(),
  kijun2021: z.number().optional(),
  kijun2022: z.number().optional(),
  kijun2023: z.number().optional(),
  kijun2024: z.number().optional(),
  kouji2012: z.number().optional(),
  kouji2013: z.number().optional(),
  kouji2014: z.number().optional(),
  kouji2015: z.number().optional(),
  kouji2016: z.number().optional(),
  kouji2017: z.number().optional(),
  kouji2018: z.number().optional(),
  kouji2019: z.number().optional(),
  kouji2020: z.number().optional(),
  kouji2021: z.number().optional(),
  kouji2022: z.number().optional(),
  kouji2023: z.number().optional(),
  kouji2024: z.number().optional(),

  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
});

export type TrEvaluationCombinedRecordsProps = z.infer<
  typeof TrEvaluationCombinedRecordsSchema
>;

export const PropertyMaterialNameMappingSchema = z.object({
  id: z.string(),
  name: z.string(),
  recordId: z.string().optional(),
  uploadUserId: z.string().optional(),
  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),

  tllUser: TllUserFormSchema.optional(),
});

export type PropertyMaterialNameMappingProps = z.infer<
  typeof PropertyMaterialNameMappingSchema
>;

export const BlogSchema = z.object({
  id: z.string(),

  creatorUserId: z.string().optional(),
  creatorUser: TllUserFormSchema.optional(),

  viewEn: z.number().optional(),
  viewJa: z.number().optional(),
  viewZh: z.number().optional(),

  buildingId: z.string().optional(),
  postalCodeId: z.string().optional(),
  stationGroupId: z.string().optional(),
  areaCode: z.number().optional(),

  building: ProBuildingSchema.optional(),
  postalCode: GeoPostalCodeSchema.optional(),
  stationGroup: RailwayStationGroupSchema.optional(),
  area: GeoAreaSchema.optional(),

  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
});
export type BlogProps = z.infer<typeof BlogSchema>;
