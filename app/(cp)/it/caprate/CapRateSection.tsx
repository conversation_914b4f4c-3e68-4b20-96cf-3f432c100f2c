"use client";

import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import SearchBarCombinedUI from "@/components/ui/SearchBarCombinedUI";
import CapRateAnalysis from "./CapRateAnalysis";

export default function CapRateSection() {
  const [selectedOption, setSelectedOption] = useState<{
    label: string;
    value: number;
    type: string;
  } | null>(null);

  console.log("🔥 CapRateSection selectedOption:", selectedOption);

  return (
    <div className="w-full">
      <div className="border-b border-neutral-200 p-2 flex flex-col items-start justify-between w-full">
        <h2 className="text-xl font-semibold text-neutral-900">相場利回査定</h2>

        <div className="text-sm text-gray-500">
          収益物件のキャップレート分析ツール - 一棟収益物件のみ対応
        </div>
      </div>

      <div className="space-y-4 p-2">
        <SearchBarCombinedUI
          selectedOption={selectedOption}
          setSelectedOption={setSelectedOption}
          recordType="BUILDING"
        />

        {!selectedOption && (
          <div className="text-center p-8 text-gray-500">
            上記の検索ボックスで場所を選択してください
          </div>
        )}

        {selectedOption && (
          <CapRateAnalysis
            key={`${selectedOption.type}-${selectedOption.value}`}
            selectedOption={selectedOption}
          />
        )}
      </div>
    </div>
  );
}
