"use client";

import { useEffect, useState } from "react";
import { getCapRateAnalysisData } from "@/actions/capRateAnalysis";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { DataTable } from "@/components/ui/data-table";
import { useAuthStore } from "@/store/auth";
import { Loader2 } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { removeOutlier } from "@/lib/helper/stats";
import dayjs from "dayjs";
import { getStatus } from "@/lib/userLambdaRecord/getStatus";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import { ChartContainer } from "@/components/ui/chart";

interface CapRateStats {
  max: number;
  min: number;
  avg: number;
  eightyPercentile: number;
  twentyPercentile: number;
  comparatorType: string;
}

export default function CapRateAnalysis({
  selectedOption,
}: {
  selectedOption: { label: string; value: number; type: string };
}) {
  const [nearbyRecords, setNearbyRecords] = useState<UserLambdaRecordProps[]>(
    [],
  );
  const [isLoading, setIsLoading] = useState(false);
  const [capRateStats, setCapRateStats] = useState<CapRateStats | null>(null);
  const [columns, setColumns] = useState<any[]>([]);
  const currentUser = useAuthStore((state) => state.currentUser);

  // Filter states (same as NearbyProperties)
  const [selectedTypeTags, setSelectedTypeTags] = useState<string>("all");
  const [selectedUpdatedTag, setSelectedUpdatedTag] = useState<string>("12");
  const [selectedBuiltYearToTag, setSelectedBuiltYearToTag] =
    useState<string>("all");
  const [selectedBuiltYearFromTag, setSelectedBuiltYearFromTag] =
    useState<string>("all");
  const [selectedStatusTag, setSelectedStatusTag] = useState<string>("all");
  const [selectedLandSize, setSelectedLandSize] = useState<string>("all");
  const [selectedBuildingSize, setSelectedBuildingSize] =
    useState<string>("all");
  const [selectedPriceMax, setSelectedPriceMax] = useState<string>("all");

  // Filter function (same as NearbyProperties)
  const filterRecords = (record: any) => {
    if (selectedStatusTag !== "all" && getStatus(record) !== selectedStatusTag)
      return false;
    if (selectedTypeTags !== "all" && record.recordSubType !== selectedTypeTags)
      return false;
    if (
      selectedUpdatedTag !== "ALL" &&
      record?.priceChanges?.length &&
      record.priceChanges.length > 0 &&
      dayjs(
        record.priceChanges[record.priceChanges.length - 1].recordDate,
      ).isBefore(dayjs().subtract(parseInt(selectedUpdatedTag), "month"))
    ) {
      return false;
    }
    if (
      selectedPriceMax !== "all" &&
      record.price &&
      record.price > parseInt(selectedPriceMax)
    ) {
      return false;
    }
    if (
      selectedLandSize !== "all" &&
      record.landSize &&
      record.landSize > parseInt(selectedLandSize)
    ) {
      return false;
    }
    if (
      selectedBuildingSize !== "all" &&
      record.buildingSize &&
      record.buildingSize > parseInt(selectedBuildingSize)
    ) {
      return false;
    }
    if (selectedBuiltYearToTag !== "all") {
      if (
        record.buildingBuiltYear &&
        dayjs().year() - record.buildingBuiltYear >
          parseInt(selectedBuiltYearToTag)
      ) {
        return false;
      }
    }
    if (selectedBuiltYearFromTag !== "all") {
      if (
        record.buildingBuiltYear &&
        dayjs().year() - record.buildingBuiltYear <
          parseInt(selectedBuiltYearFromTag)
      ) {
        return false;
      }
    }
    return true;
  };

  const calculateCapRateStats = (
    records: UserLambdaRecordProps[],
  ): CapRateStats => {
    const capRates = records
      .map((record) => {
        if (record.yearlyIncome && record.price && record.price > 0) {
          return (record.yearlyIncome / record.price) * 100;
        }
        return null;
      })
      .filter((rate): rate is number => rate !== null && !isNaN(rate))
      .sort((a, b) => a - b);

    if (capRates.length === 0) {
      return {
        max: 0,
        min: 0,
        avg: 0,
        eightyPercentile: 0,
        twentyPercentile: 0,
        comparatorType: "Cap Rate (%)",
      };
    }

    // Remove outliers before calculation
    const cleanedCapRates = removeOutlier(capRates);

    const min = Math.min(...cleanedCapRates);
    const max = Math.max(...cleanedCapRates);
    const avg =
      cleanedCapRates.reduce((sum, rate) => sum + rate, 0) /
      cleanedCapRates.length;
    const twentyPercentile =
      cleanedCapRates[Math.floor(cleanedCapRates.length * 0.2)] || 0;
    const eightyPercentile =
      cleanedCapRates[Math.floor(cleanedCapRates.length * 0.8)] || 0;

    return {
      max: parseFloat(max.toFixed(2)),
      min: parseFloat(min.toFixed(2)),
      avg: parseFloat(avg.toFixed(2)),
      eightyPercentile: parseFloat(eightyPercentile.toFixed(2)),
      twentyPercentile: parseFloat(twentyPercentile.toFixed(2)),
      comparatorType: "Cap Rate (%)",
    };
  };

  const fetchCapRateData = async () => {
    setIsLoading(true);
    // Clear previous data when starting new search
    setNearbyRecords([]);
    setCapRateStats(null);

    try {
      const response = await getCapRateAnalysisData({ selectedOption });
      if (response.success && response.data) {
        setNearbyRecords(response.data.nearbyRecords);
        const stats = calculateCapRateStats(response.data.nearbyRecords);
        setCapRateStats(stats);
      }
    } catch (error) {
      console.error("🔥 Failed to fetch cap rate data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Generate chart data
  const generateCapRateDistributionData = (
    records: UserLambdaRecordProps[],
  ) => {
    const filteredRecords = records.filter(filterRecords);
    const capRates = filteredRecords
      .map((record) => {
        if (record.yearlyIncome && record.price && record.price > 0) {
          return (record.yearlyIncome / record.price) * 100;
        }
        return null;
      })
      .filter((rate): rate is number => rate !== null && !isNaN(rate));

    // Create distribution buckets
    const buckets = [
      { range: "0-2%", min: 0, max: 2, count: 0 },
      { range: "2-4%", min: 2, max: 4, count: 0 },
      { range: "4-6%", min: 4, max: 6, count: 0 },
      { range: "6-8%", min: 6, max: 8, count: 0 },
      { range: "8-10%", min: 8, max: 10, count: 0 },
      { range: "10%+", min: 10, max: Infinity, count: 0 },
    ];

    capRates.forEach((rate) => {
      const bucket = buckets.find((b) => rate >= b.min && rate < b.max);
      if (bucket) bucket.count++;
    });

    return buckets;
  };

  const generatePriceVsCapRateData = (records: UserLambdaRecordProps[]) => {
    const filteredRecords = records.filter(filterRecords);
    return filteredRecords
      .map((record) => {
        if (record.yearlyIncome && record.price && record.price > 0) {
          return {
            price: record.price,
            capRate: (record.yearlyIncome / record.price) * 100,
            address: record.address,
          };
        }
        return null;
      })
      .filter((item): item is NonNullable<typeof item> => item !== null)
      .slice(0, 50); // Limit for performance
  };

  useEffect(() => {
    if (selectedOption?.value) {
      fetchCapRateData();
    } else {
      // Clear data when no option is selected
      setNearbyRecords([]);
      setCapRateStats(null);
    }
  }, [selectedOption?.value, selectedOption?.type]);

  // Recalculate stats when filters change
  useEffect(() => {
    if (nearbyRecords.length > 0) {
      const filteredRecords = nearbyRecords.filter(filterRecords);
      const stats = calculateCapRateStats(filteredRecords);
      setCapRateStats(stats);
    }
  }, [
    nearbyRecords,
    selectedStatusTag,
    selectedTypeTags,
    selectedUpdatedTag,
    selectedBuiltYearToTag,
    selectedBuiltYearFromTag,
    selectedLandSize,
    selectedBuildingSize,
    selectedPriceMax,
  ]);

  console.log("selectedOption", selectedOption);

  // Create simplified columns for cap rate analysis
  const getCapRateAnalysisColumns = () => {
    return [
      {
        header: "距離",
        cell: ({ row }: { row: any }) => {
          return (
            <div className="text-xs">
              {row.original.distance
                ? `${(row.original.distance / 1000).toFixed(1)}km`
                : "-"}
            </div>
          );
        },
      },
      {
        header: "住所",
        cell: ({ row }: { row: any }) => {
          return (
            <div className="text-center text-xs truncate">
              {row.original.address}
            </div>
          );
        },
      },
      {
        header: "タイプ",
        cell: ({ row }: { row: any }) => {
          return (
            <div className="text-xs">{row.original.recordSubType || "-"}</div>
          );
        },
      },
      {
        header: "価格",
        cell: ({ row }: { row: any }) => {
          return (
            <div className="text-xs font-medium">
              {row.original.price
                ? `${row.original.price.toLocaleString()}万円`
                : "-"}
            </div>
          );
        },
      },
      {
        header: "年収入",
        cell: ({ row }: { row: any }) => {
          return (
            <div className="text-xs">
              {row.original.yearlyIncome
                ? `${row.original.yearlyIncome.toLocaleString()}万円`
                : "-"}
            </div>
          );
        },
      },
      {
        header: "利回り",
        cell: ({ row }: { row: any }) => {
          if (
            row.original.yearlyIncome &&
            row.original.price &&
            row.original.price > 0
          ) {
            const capRate = (
              (row.original.yearlyIncome / row.original.price) *
              100
            ).toFixed(2);
            return (
              <div className="text-xs font-bold text-blue-600">{capRate}%</div>
            );
          }
          return <div className="text-xs">-</div>;
        },
      },
      {
        header: "建物面積",
        cell: ({ row }: { row: any }) => {
          return (
            <div className="text-xs">
              {row.original.buildingSize
                ? `${row.original.buildingSize}㎡`
                : "-"}
            </div>
          );
        },
      },
      {
        header: "築年数",
        cell: ({ row }: { row: any }) => {
          if (row.original.buildingBuiltYear) {
            const age =
              new Date().getFullYear() - row.original.buildingBuiltYear;
            return <div className="text-xs">{age}年</div>;
          }
          return <div className="text-xs">-</div>;
        },
      },
      {
        header: "最寄駅",
        cell: ({ row }: { row: any }) => {
          return (
            <div className="text-xs max-w-[120px] truncate">
              {row.original.nearestStation || "-"}
            </div>
          );
        },
      },
    ];
  };

  useEffect(() => {
    setColumns(getCapRateAnalysisColumns());
  }, []);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="w-6 h-6 animate-spin mr-2" />
        <span>データを読み込み中...</span>
      </div>
    );
  }

  if (!capRateStats || nearbyRecords.length === 0) {
    return (
      <div className="text-center p-8 text-gray-500">
        該当する建物データが見つかりませんでした
      </div>
    );
  }

  const filteredRecords = nearbyRecords.filter(filterRecords);

  return (
    <div className="space-y-6">
      {/* Filters - Single Row */}
      <div className="flex flex-wrap gap-2 items-center">
        <Select value={selectedStatusTag} onValueChange={setSelectedStatusTag}>
          <SelectTrigger className="w-[140px]">
            <SelectValue placeholder="販売状況" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">販売状況:全て</SelectItem>
            <SelectItem value="公開中">公開中のみ</SelectItem>
            <SelectItem value="成約">成約のみ</SelectItem>
          </SelectContent>
        </Select>

        <Select value={selectedTypeTags} onValueChange={setSelectedTypeTags}>
          <SelectTrigger className="w-[120px]">
            <SelectValue placeholder="タイプ" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">タイプ: 全て</SelectItem>
            <SelectItem value="アパート">アパート</SelectItem>
            <SelectItem value="マンション">マンション</SelectItem>
            <SelectItem value="ビル">ビル</SelectItem>
            <SelectItem value="店舗">店舗</SelectItem>
          </SelectContent>
        </Select>

        <Select
          value={selectedUpdatedTag}
          onValueChange={setSelectedUpdatedTag}
        >
          <SelectTrigger className="w-[120px]">
            <SelectValue placeholder="更新期間" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="ALL">全期間</SelectItem>
            <SelectItem value="3">3ヶ月以内</SelectItem>
            <SelectItem value="6">6ヶ月以内</SelectItem>
            <SelectItem value="12">12ヶ月以内</SelectItem>
          </SelectContent>
        </Select>

        <Select value={selectedPriceMax} onValueChange={setSelectedPriceMax}>
          <SelectTrigger className="w-[140px]">
            <SelectValue placeholder="価格上限" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">価格: 制限なし</SelectItem>
            <SelectItem value="5000">5,000万円以下</SelectItem>
            <SelectItem value="10000">1億円以下</SelectItem>
            <SelectItem value="30000">3億円以下</SelectItem>
          </SelectContent>
        </Select>

        <Select
          value={selectedBuildingSize}
          onValueChange={setSelectedBuildingSize}
        >
          <SelectTrigger className="w-[140px]">
            <SelectValue placeholder="建物面積" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">面積: 制限なし</SelectItem>
            <SelectItem value="200">200㎡以下</SelectItem>
            <SelectItem value="500">500㎡以下</SelectItem>
            <SelectItem value="1000">1,000㎡以下</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Cap Rate Statistics Line */}
      <div className="bg-neutral-100 p-4 rounded-lg">
        <div className="mb-4">
          <h3 className="text-lg font-semibold mb-2">相場利回査定結果</h3>
          <div className="relative">
            {/* Line visualization */}
            <div className="h-8 bg-gradient-to-r from-gray-200 to-gray-300 relative">
              {(() => {
                const range = capRateStats.max - capRateStats.min;
                const getPosition = (value: number) => {
                  if (range === 0) return 0;
                  return ((value - capRateStats.min) / range) * 100;
                };

                return (
                  <>
                    {/* Min marker */}
                    <div
                      className="absolute top-0 h-8 w-1 bg-gray-600 rounded"
                      style={{ left: `${getPosition(capRateStats.min)}%` }}
                    />
                    {/* 20% marker */}
                    <div
                      className="absolute top-0 h-8 w-1 bg-gray-700 rounded"
                      style={{
                        left: `${getPosition(capRateStats.twentyPercentile)}%`,
                      }}
                    />
                    {/* Average marker */}
                    <div
                      className="absolute top-0 h-8 w-1 bg-blue-600 rounded"
                      style={{ left: `${getPosition(capRateStats.avg)}%` }}
                    />
                    {/* 80% marker */}
                    <div
                      className="absolute top-0 h-8 w-1 bg-gray-700 rounded"
                      style={{
                        left: `${getPosition(capRateStats.eightyPercentile)}%`,
                      }}
                    />
                    {/* Max marker */}
                    <div
                      className="absolute top-0 h-8 w-1 bg-gray-600 rounded"
                      style={{ left: `${getPosition(capRateStats.max)}%` }}
                    />
                  </>
                );
              })()}
            </div>

            {/* Labels */}
            <div className="flex justify-between mt-2 text-sm">
              <div className="text-center">
                <div className="font-bold text-gray-700">
                  {capRateStats.min}%
                </div>
                <div className="text-gray-500">最小値</div>
              </div>
              <div className="text-center">
                <div className="font-bold text-gray-700">
                  {capRateStats.twentyPercentile}%
                </div>
                <div className="text-gray-500">20% Percentile</div>
              </div>
              <div className="text-center text-lg">
                <div className="font-bold text-blue-600">
                  {capRateStats.avg}%
                </div>
                <div className="text-gray-500">平均</div>
              </div>
              <div className="text-center">
                <div className="font-bold text-gray-700">
                  {capRateStats.eightyPercentile}%
                </div>
                <div className="text-gray-500">80% Percentile</div>
              </div>
              <div className="text-center">
                <div className="font-bold text-gray-700">
                  {capRateStats.max}%
                </div>
                <div className="text-gray-500">最大値</div>
              </div>
            </div>
          </div>
        </div>
        <div className="text-sm text-gray-600 text-center">
          検索範囲: 5km以内 | 対象件数: {filteredRecords.length}件 (全
          {nearbyRecords.length}件中)
        </div>
      </div>

      {/* Table and Chart - Single Row */}
      <div className="grid grid-cols-4 gap-6">
        {/* Data Table - 3 grids */}
        <div className="col-span-3">
          <div className="text-lg mb-2">近隣建物データ</div>
          <div className="text-xs text-gray-500 mb-2">
            合計: {filteredRecords.length}件
          </div>

          <DataTable
            columns={columns}
            data={filteredRecords}
            defaultPageSize={100}
            showFooter={true}
            isLoading={isLoading}
          />
        </div>

        {/* Chart - 1 grid */}
        <div className="col-span-1 w-full">
          <div>利回りチャート</div>
          <ChartContainer config={{}} className="h-[400px] w-full">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={generateCapRateDistributionData(nearbyRecords)}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="range" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="count" fill="#4AC1FF" />
              </BarChart>
            </ResponsiveContainer>
          </ChartContainer>
        </div>
      </div>
    </div>
  );
}
