"use client";

import { But<PERSON> } from "@/components/ui/button";
import { CustomerFollowUpRecordProps } from "@/lib/definitions";
import { Eye, Pencil, Trash } from "lucide-react";
import Link from "next/link";
import dayjs from "dayjs";
import { deleteFollowUpRecordAction } from "@/actions/customerFollowUpRecords";
import { toast } from "@/hooks/use-toast";

export const followUpColumns = [
  {
    accessorKey: "customer.name",
    header: "顧客名",
    cell: ({ row }: { row: any }) => {
      const record = row.original as CustomerFollowUpRecordProps;
      return (
        <div className="font-medium">
          <Link
            href={`/pa/customer/${record.customerId}`}
            className="hover:underline text-blue-600"
          >
            {record.customer?.name || "-"}
          </Link>
        </div>
      );
    },
  },
  {
    accessorKey: "agentUser.name",
    header: "担当者",
    cell: ({ row }: { row: any }) => {
      const record = row.original as CustomerFollowUpRecordProps;
      return <div className="text-sm">{record.agentUser?.name || "-"}</div>;
    },
  },
  {
    accessorKey: "followUpDate",
    header: "フォローアップ日",
    cell: ({ row }: { row: any }) => {
      const record = row.original as CustomerFollowUpRecordProps;
      return (
        <div className="text-sm">
          {record.followUpDate
            ? dayjs(record.followUpDate).format("YYYY/MM/DD")
            : "-"}
        </div>
      );
    },
  },
  {
    accessorKey: "comments",
    header: "コメント",
    cell: ({ row }: { row: any }) => {
      const record = row.original as CustomerFollowUpRecordProps;
      return (
        <div className="text-sm max-w-xs truncate">
          {record.comments || "-"}
        </div>
      );
    },
  },
  {
    accessorKey: "createdAt",
    header: "作成日",
    cell: ({ row }: { row: any }) => {
      const record = row.original as CustomerFollowUpRecordProps;
      return (
        <div className="text-sm">
          {record.createdAt
            ? dayjs(record.createdAt).format("YYYY/MM/DD HH:mm")
            : "-"}
        </div>
      );
    },
  },
  {
    id: "actions",
    header: "操作",
    cell: ({ row }: { row: any }) => {
      const record = row.original as CustomerFollowUpRecordProps;

      const handleDelete = async () => {
        if (!record.id) return;

        if (confirm("このフォローアップ記録を削除しますか？")) {
          try {
            const result = await deleteFollowUpRecordAction(record.id);
            if (result.success) {
              toast({
                title: "削除完了",
                description: "フォローアップ記録を削除しました",
              });
              // Refresh the page or update the data
              window.location.reload();
            } else {
              toast({
                title: "削除エラー",
                description: result.message || "削除に失敗しました",
                variant: "destructive",
              });
            }
          } catch (error) {
            toast({
              title: "削除エラー",
              description: "削除中にエラーが発生しました",
              variant: "destructive",
            });
          }
        }
      };

      return (
        <div className="flex flex-row gap-2">
          <Button
            variant="destructive"
            size="sm"
            onClick={handleDelete}
            className=""
          >
            <Trash className="w-4 h-4" />
          </Button>
        </div>
      );
    },
  },
];
