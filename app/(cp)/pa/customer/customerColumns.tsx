"use client";

import { But<PERSON> } from "@/components/ui/button";
import { CustomerProps } from "@/lib/definitions";
import { Eye, Pencil } from "lucide-react";
import Link from "next/link";
import { Badge } from "@/components/ui/badge";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import FollowUpSheet from "./FollowUpSheet";

dayjs.extend(relativeTime);

export const customerColumns = [
  {
    accessorKey: "name",
    header: "顧客名",
    cell: ({ row }: { row: any }) => {
      const customer = row.original as CustomerProps;
      return <div className="font-medium">{customer.name}</div>;
    },
  },
  {
    accessorKey: "agentUser.name",
    header: "担当者",
    cell: ({ row }: { row: any }) => {
      const customer = row.original as CustomerProps;
      return (
        <div className="text-sm">
          {customer.agentUser?.name || "未割り当て"}
        </div>
      );
    },
  },
  {
    accessorKey: "email",
    header: "メール",
    cell: ({ row }: { row: any }) => {
      const customer = row.original as CustomerProps;
      return <div className="text-sm">{customer.email || "-"}</div>;
    },
  },
  {
    accessorKey: "phone",
    header: "電話番号",
    cell: ({ row }: { row: any }) => {
      const customer = row.original as CustomerProps;
      return <div className="text-sm">{customer.phone || "-"}</div>;
    },
  },
  {
    accessorKey: "address",
    header: "住所",
    cell: ({ row }: { row: any }) => {
      const customer = row.original as CustomerProps;
      return <div className="text-sm">{customer.address || "-"}</div>;
    },
  },
  {
    accessorKey: "age",
    header: "年齢",
    cell: ({ row }: { row: any }) => {
      const customer = row.original as CustomerProps;
      return (
        <div className="text-sm">
          {customer.age ? `${customer.age}歳` : "-"}
        </div>
      );
    },
  },
  {
    accessorKey: "income",
    header: "収入",
    cell: ({ row }: { row: any }) => {
      const customer = row.original as CustomerProps;
      return (
        <div className="text-sm">
          {customer.income ? `¥${customer.income.toLocaleString()}` : "-"}
        </div>
      );
    },
  },
  {
    accessorKey: "needs",
    header: "ニーズ数",
    cell: ({ row }: { row: any }) => {
      const customer = row.original as CustomerProps;
      return <Badge variant="outline">{customer.needs?.length || 0}</Badge>;
    },
  },
  {
    accessorKey: "lastFollowUpDate",
    header: "最終フォローアップ",
    cell: ({ row }: { row: any }) => {
      const customer = row.original as any; // Extended CustomerProps with followUpRecords
      // Find the most recent follow-up record
      const lastFollowUp = customer.followUpRecords?.reduce(
        (latest: any, current: any) => {
          if (!latest) return current;
          return dayjs(current.followUpDate).isAfter(dayjs(latest.followUpDate))
            ? current
            : latest;
        },
        null,
      );

      if (!lastFollowUp) {
        return <div className="text-sm text-neutral-500">-</div>;
      }

      const followUpDate = dayjs(lastFollowUp.followUpDate);
      const daysAgo = dayjs().diff(followUpDate, "day");

      return (
        <div className="text-sm">
          <div>{followUpDate.format("YYYY/MM/DD")}</div>
          <div className="text-xs text-neutral-500">
            {daysAgo === 0 ? "今日" : `${daysAgo}日前`}
          </div>
          <div className="text-xs text-neutral-600">
            by {lastFollowUp.agentUser?.name || "不明"}
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: "createdAt",
    header: "登録日",
    cell: ({ row }: { row: any }) => {
      const customer = row.original as any; // Extended CustomerProps with createdAt
      return (
        <div className="text-sm">
          {customer.createdAt
            ? dayjs(customer.createdAt).format("YYYY/MM/DD")
            : "-"}
        </div>
      );
    },
  },
  {
    id: "actions",
    header: "操作",
    cell: ({ row }: { row: any }) => {
      const customer = row.original as CustomerProps;
      return (
        <div className="flex flex-row gap-2">
          <Link href={`/pa/customer/${customer.id}/edit`}>
            <Button variant="outline" size="sm">
              <Pencil className="w-4 h-4" />
            </Button>
          </Link>
          <Link href={`/pa/customer/${customer.id}`}>
            <Button variant="outline" size="sm">
              <Eye className="w-4 h-4" />
            </Button>
          </Link>
          <FollowUpSheet
            customerId={customer.id || ""}
            customerName={customer.name || ""}
          />
        </div>
      );
    },
  },
];
