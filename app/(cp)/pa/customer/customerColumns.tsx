"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  CustomerProps,
  TllCustomerMainNeedType,
  TllCustomerSource,
} from "@/lib/definitions";
import { Eye, Pencil } from "lucide-react";
import Link from "next/link";
import { Badge } from "@/components/ui/badge";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import FollowUpSheet from "./FollowUpSheet";

dayjs.extend(relativeTime);

// Helper functions for labels and colors
const getMainNeedTypeLabel = (value: string) => {
  const labels: Record<string, string> = {
    [TllCustomerMainNeedType.RENTAL]: "賃貸",
    [TllCustomerMainNeedType.INVESTMENT_BUILDING]: "投資・一棟",
    [TllCustomerMainNeedType.INVESTMENT_UNIT]: "投資・区分",
    [TllCustomerMainNeedType.INVESTMENT_HOUSE]: "投資・戸建",
    [TllCustomerMainNeedType.INVESTMENT_LAND]: "投資・土地",
    [TllCustomerMainNeedType.OWNER_OCCUPIED_UNIT]: "実需・区分",
    [TllCustomerMainNeedType.OWNER_OCCUPIED_HOUSE]: "実需・戸建",
    [TllCustomerMainNeedType.OWNER_OCCUPIED_LAND]: "実需・土地",
  };
  return labels[value] || value;
};

const getMainNeedTypeColor = (value: string) => {
  const colors: Record<string, string> = {
    [TllCustomerMainNeedType.RENTAL]:
      "bg-blue-100 text-blue-800 border-blue-200",
    [TllCustomerMainNeedType.INVESTMENT_BUILDING]:
      "bg-green-100 text-green-800 border-green-200",
    [TllCustomerMainNeedType.INVESTMENT_UNIT]:
      "bg-green-100 text-green-800 border-green-200",
    [TllCustomerMainNeedType.INVESTMENT_HOUSE]:
      "bg-green-100 text-green-800 border-green-200",
    [TllCustomerMainNeedType.OWNER_OCCUPIED_UNIT]:
      "bg-purple-100 text-purple-800 border-purple-200",
    [TllCustomerMainNeedType.OWNER_OCCUPIED_HOUSE]:
      "bg-purple-100 text-purple-800 border-purple-200",
  };
  return colors[value] || "bg-gray-100 text-gray-800 border-gray-200";
};

const getSourceLabel = (value: string) => {
  const labels: Record<string, string> = {
    [TllCustomerSource.URBALYTICS]: "Urbalytics",
    [TllCustomerSource.TLL_REFERAL]: "TLL紹介",
    [TllCustomerSource.AGENT_REFERAL]: "エージェント紹介",
    [TllCustomerSource.RAKUMACHI]: "楽待",
    [TllCustomerSource.KENBIYA]: "Kenbiya",
    [TllCustomerSource.ATHOME_SUUMO]: "Athome/Suumo",
    [TllCustomerSource.XIAOHONGSHU]: "小红书",
    [TllCustomerSource.WECHAT_GROUP]: "微信群",
  };
  return labels[value] || value;
};

const getSourceColor = (value: string) => {
  const colors: Record<string, string> = {
    [TllCustomerSource.URBALYTICS]:
      "bg-indigo-100 text-indigo-800 border-indigo-200",
    [TllCustomerSource.TLL_REFERAL]:
      "bg-orange-100 text-orange-800 border-orange-200",
    [TllCustomerSource.AGENT_REFERAL]:
      "bg-yellow-100 text-yellow-800 border-yellow-200",
    [TllCustomerSource.RAKUMACHI]: "bg-red-100 text-red-800 border-red-200",
    [TllCustomerSource.KENBIYA]: "bg-teal-100 text-teal-800 border-teal-200",
    [TllCustomerSource.ATHOME_SUUMO]:
      "bg-cyan-100 text-cyan-800 border-cyan-200",
    [TllCustomerSource.XIAOHONGSHU]:
      "bg-pink-100 text-pink-800 border-pink-200",
    [TllCustomerSource.WECHAT_GROUP]:
      "bg-emerald-100 text-emerald-800 border-emerald-200",
  };
  return colors[value] || "bg-gray-100 text-gray-800 border-gray-200";
};

export const createCustomerColumns = (onFollowUpSuccess?: () => void) => [
  {
    accessorKey: "name",
    header: "顧客名",
    cell: ({ row }: { row: any }) => {
      const customer = row.original as CustomerProps;
      return <div className="font-medium">{customer.name}</div>;
    },
  },
  {
    accessorKey: "agentUser.name",
    header: "担当者",
    cell: ({ row }: { row: any }) => {
      const customer = row.original as CustomerProps;
      return <div className="text-sm">{customer.agentUser?.name || "-"}</div>;
    },
  },
  {
    accessorKey: "email",
    header: "メール",
    cell: ({ row }: { row: any }) => {
      const customer = row.original as CustomerProps;
      return <div className="text-sm">{customer.email || "-"}</div>;
    },
  },
  {
    accessorKey: "phone",
    header: "電話番号",
    cell: ({ row }: { row: any }) => {
      const customer = row.original as CustomerProps;
      return <div className="text-sm">{customer.phone || "-"}</div>;
    },
  },
  {
    accessorKey: "address",
    header: "住所",
    cell: ({ row }: { row: any }) => {
      const customer = row.original as CustomerProps;
      return <div className="text-sm">{customer.address || "-"}</div>;
    },
  },
  {
    accessorKey: "age",
    header: "年齢",
    cell: ({ row }: { row: any }) => {
      const customer = row.original as CustomerProps;
      return (
        <div className="text-sm">
          {customer.age ? `${customer.age}歳` : "-"}
        </div>
      );
    },
  },
  {
    accessorKey: "income",
    header: "収入",
    cell: ({ row }: { row: any }) => {
      const customer = row.original as CustomerProps;
      return (
        <div className="text-sm">
          {customer.income ? `¥${customer.income.toLocaleString()}` : "-"}
        </div>
      );
    },
  },
  {
    accessorKey: "needs",
    header: "ニーズ数",
    cell: ({ row }: { row: any }) => {
      const customer = row.original as CustomerProps;
      const needsCount = customer.needs?.length || 0;

      if (needsCount === 0) {
        return <div className="text-sm text-neutral-400">-</div>;
      }

      return <Badge variant="outline">{needsCount}</Badge>;
    },
  },
  {
    accessorKey: "mainNeedType",
    header: "メインニーズ",
    cell: ({ row }: { row: any }) => {
      const customer = row.original as CustomerProps;

      if (!customer.mainNeedType) {
        return <div className="text-sm text-neutral-400">-</div>;
      }

      return (
        <Badge
          variant="outline"
          className={`text-xs ${getMainNeedTypeColor(customer.mainNeedType)}`}
        >
          {getMainNeedTypeLabel(customer.mainNeedType)}
        </Badge>
      );
    },
  },
  {
    accessorKey: "source",
    header: "ソース",
    cell: ({ row }: { row: any }) => {
      const customer = row.original as CustomerProps;

      if (!customer.source) {
        return <div className="text-sm text-neutral-400">-</div>;
      }

      return (
        <Badge
          variant="outline"
          className={`text-xs ${getSourceColor(customer.source)}`}
        >
          {getSourceLabel(customer.source)}
        </Badge>
      );
    },
  },
  {
    accessorKey: "lastFollowUpDate",
    header: "最終フォローアップ",
    cell: ({ row }: { row: any }) => {
      const customer = row.original as any; // Extended CustomerProps with followUpRecords
      const followUpRecords = customer.followUpRecords || [];
      const totalRecords = followUpRecords.length;

      // Find the most recent follow-up record
      const lastFollowUp = followUpRecords.reduce(
        (latest: any, current: any) => {
          if (!latest) return current;
          return dayjs(current.followUpDate).isAfter(dayjs(latest.followUpDate))
            ? current
            : latest;
        },
        null,
      );

      if (!lastFollowUp) {
        return (
          <div className="text-sm text-neutral-500">
            <div>-</div>
            <div className="text-xs">記録数: {totalRecords}</div>
          </div>
        );
      }

      const followUpDate = dayjs(lastFollowUp.followUpDate);
      const daysAgo = dayjs().diff(followUpDate, "day");

      return (
        <div className="text-sm">
          <div>{followUpDate.format("YYYY/MM/DD")}</div>
          <div className="text-xs text-neutral-500">
            {daysAgo === 0 ? "今日" : `${daysAgo}日前`} | 担当:{" "}
            {lastFollowUp.agentUser?.name || "不明"}
          </div>
          <div className="text-xs text-blue-600 font-medium">
            記録数: {totalRecords}
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: "createdAt",
    header: "登録日",
    cell: ({ row }: { row: any }) => {
      const customer = row.original as any; // Extended CustomerProps with createdAt
      return (
        <div className="text-sm">
          {customer.createdAt
            ? dayjs(customer.createdAt).format("YYYY/MM/DD")
            : "-"}
        </div>
      );
    },
  },
  {
    id: "actions",
    header: "操作",
    cell: ({ row }: { row: any }) => {
      const customer = row.original as CustomerProps;
      return (
        <div className="flex flex-row gap-2">
          <Link href={`/pa/customer/${customer.id}/edit`}>
            <Button variant="outline" size="sm">
              <Pencil className="w-4 h-4" />
            </Button>
          </Link>
          <Link href={`/pa/customer/${customer.id}`}>
            <Button variant="outline" size="sm">
              <Eye className="w-4 h-4" />
            </Button>
          </Link>
          <FollowUpSheet
            customerId={customer.id || ""}
            customerName={customer.name || ""}
            onSuccess={onFollowUpSuccess}
          />
        </div>
      );
    },
  },
];

// Export default columns for backward compatibility
export const customerColumns = createCustomerColumns();
