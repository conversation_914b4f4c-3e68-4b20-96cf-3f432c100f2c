"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  CustomerProps,
  TllCustomerMainNeedType,
  TllCustomerSource,
  TllCustomerLanguage,
} from "@/lib/definitions";
import { Eye, Pencil } from "lucide-react";
import Link from "next/link";
import { Badge } from "@/components/ui/badge";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import FollowUpSheet from "./FollowUpSheet";

dayjs.extend(relativeTime);

// Helper functions for labels and colors
const getMainNeedTypeLabel = (value: string) => {
  const labels: Record<string, string> = {
    [TllCustomerMainNeedType.RENTAL]: "賃貸",
    [TllCustomerMainNeedType.INVESTMENT_BUILDING]: "投資・一棟",
    [TllCustomerMainNeedType.INVESTMENT_UNIT]: "投資・区分",
    [TllCustomerMainNeedType.INVESTMENT_HOUSE]: "投資・戸建",
    [TllCustomerMainNeedType.INVESTMENT_LAND]: "投資・土地",
    [TllCustomerMainNeedType.OWNER_OCCUPIED_UNIT]: "実需・区分",
    [TllCustomerMainNeedType.OWNER_OCCUPIED_HOUSE]: "実需・戸建",
    [TllCustomerMainNeedType.OWNER_OCCUPIED_LAND]: "実需・土地",
  };
  return labels[value] || value;
};

const getMainNeedTypeColor = (value: string) => {
  const colors: Record<string, string> = {
    [TllCustomerMainNeedType.RENTAL]:
      "bg-blue-100 text-blue-800 border-blue-200",
    [TllCustomerMainNeedType.INVESTMENT_BUILDING]:
      "bg-green-100 text-green-800 border-green-200",
    [TllCustomerMainNeedType.INVESTMENT_UNIT]:
      "bg-green-100 text-green-800 border-green-200",
    [TllCustomerMainNeedType.INVESTMENT_HOUSE]:
      "bg-green-100 text-green-800 border-green-200",
    [TllCustomerMainNeedType.INVESTMENT_LAND]:
      "bg-green-100 text-green-800 border-green-200",
    [TllCustomerMainNeedType.OWNER_OCCUPIED_UNIT]:
      "bg-purple-100 text-purple-800 border-purple-200",
    [TllCustomerMainNeedType.OWNER_OCCUPIED_HOUSE]:
      "bg-purple-100 text-purple-800 border-purple-200",
    [TllCustomerMainNeedType.OWNER_OCCUPIED_LAND]:
      "bg-purple-100 text-purple-800 border-purple-200",
  };
  return colors[value] || "bg-gray-100 text-gray-800 border-gray-200";
};

const getSourceLabel = (value: string) => {
  const labels: Record<string, string> = {
    [TllCustomerSource.URBALYTICS]: "Urbalytics",
    [TllCustomerSource.TLL_REFERAL]: "TLL紹介",
    [TllCustomerSource.AGENT_REFERAL]: "エージェント紹介",
    [TllCustomerSource.RAKUMACHI]: "楽待",
    [TllCustomerSource.KENBIYA]: "Kenbiya",
    [TllCustomerSource.ATHOME_SUUMO]: "Athome/Suumo",
    [TllCustomerSource.XIAOHONGSHU]: "小红书",
    [TllCustomerSource.WECHAT_GROUP]: "微信群",
  };
  return labels[value] || value;
};

const getSourceColor = (value: string) => {
  const colors: Record<string, string> = {
    [TllCustomerSource.URBALYTICS]:
      "bg-indigo-100 text-indigo-800 border-indigo-200",
    [TllCustomerSource.TLL_REFERAL]:
      "bg-orange-100 text-orange-800 border-orange-200",
    [TllCustomerSource.AGENT_REFERAL]:
      "bg-yellow-100 text-yellow-800 border-yellow-200",
    [TllCustomerSource.RAKUMACHI]: "bg-red-100 text-red-800 border-red-200",
    [TllCustomerSource.KENBIYA]: "bg-teal-100 text-teal-800 border-teal-200",
    [TllCustomerSource.ATHOME_SUUMO]:
      "bg-cyan-100 text-cyan-800 border-cyan-200",
    [TllCustomerSource.XIAOHONGSHU]:
      "bg-pink-100 text-pink-800 border-pink-200",
    [TllCustomerSource.WECHAT_GROUP]:
      "bg-emerald-100 text-emerald-800 border-emerald-200",
  };
  return colors[value] || "bg-gray-100 text-gray-800 border-gray-200";
};

const getLanguageLabel = (value: string) => {
  const labels: Record<string, string> = {
    [TllCustomerLanguage.ENGLISH]: "English",
    [TllCustomerLanguage.JAPANESE]: "日本語",
    [TllCustomerLanguage.CHINESE]: "中文",
  };
  return labels[value] || value;
};

const getLanguageColor = (value: string) => {
  const colors: Record<string, string> = {
    [TllCustomerLanguage.ENGLISH]: "bg-blue-100 text-blue-800 border-blue-200",
    [TllCustomerLanguage.JAPANESE]: "bg-red-100 text-red-800 border-red-200",
    [TllCustomerLanguage.CHINESE]:
      "bg-yellow-100 text-yellow-800 border-yellow-200",
  };
  return colors[value] || "bg-gray-100 text-gray-800 border-gray-200";
};

export const createCustomerColumns = (onFollowUpSuccess?: () => void) => [
  {
    accessorKey: "name",
    header: "顧客名",
    cell: ({ row }: { row: any }) => {
      const customer = row.original as CustomerProps;
      return <div className="font-medium">{customer.name}</div>;
    },
  },
  {
    accessorKey: "agentUser",
    header: "担当者",
    cell: ({ row }: { row: any }) => {
      const customer = row.original as any;
      return <div className="text-sm">{customer.agentUser?.name || "-"}</div>;
    },
  },
  {
    accessorKey: "contact",
    header: "連絡方法",
    cell: ({ row }: { row: any }) => {
      const customer = row.original as CustomerProps;
      const contacts = [];

      if (customer.email) contacts.push(`📧 ${customer.email}`);
      if (customer.phone) contacts.push(`📞 ${customer.phone}`);
      if (customer.wechatAccount) contacts.push(`💬 ${customer.wechatAccount}`);

      if (contacts.length === 0) {
        return <div className="text-sm text-neutral-400">-</div>;
      }

      return (
        <div className="text-xs space-y-1">
          {contacts.map((contact, index) => (
            <div key={index} className="truncate max-w-[200px]" title={contact}>
              {contact}
            </div>
          ))}
        </div>
      );
    },
  },
  {
    accessorKey: "financialInfo",
    header: "財務情報",
    cell: ({ row }: { row: any }) => {
      const customer = row.original as CustomerProps;
      const info = [];

      if (customer.income)
        info.push(`年収: ¥${customer.income.toLocaleString()}`);
      if (customer.financialAsset)
        info.push(`資産: ¥${customer.financialAsset.toLocaleString()}`);
      if (customer.propertyOwned) info.push(`所有: ${customer.propertyOwned}`);

      if (info.length === 0) {
        return <div className="text-sm text-neutral-400">-</div>;
      }

      return (
        <div className="text-xs space-y-1">
          {info.map((item, index) => (
            <div key={index} className="truncate max-w-[150px]" title={item}>
              {item}
            </div>
          ))}
        </div>
      );
    },
  },
  {
    accessorKey: "language",
    header: "言語",
    cell: ({ row }: { row: any }) => {
      const customer = row.original as CustomerProps;

      return (
        <Badge
          variant="outline"
          className={`text-xs ${getLanguageColor(customer.language || TllCustomerLanguage.CHINESE)}`}
        >
          {getLanguageLabel(customer.language || TllCustomerLanguage.CHINESE)}
        </Badge>
      );
    },
  },
  {
    accessorKey: "mainNeedType",
    header: "メインニーズ",
    cell: ({ row }: { row: any }) => {
      const customer = row.original as CustomerProps;

      return (
        <Badge
          variant="outline"
          className={`text-xs ${getMainNeedTypeColor(customer.mainNeedType || TllCustomerMainNeedType.INVESTMENT_BUILDING)}`}
        >
          {getMainNeedTypeLabel(
            customer.mainNeedType ||
              TllCustomerMainNeedType.INVESTMENT_BUILDING,
          )}
        </Badge>
      );
    },
  },
  {
    accessorKey: "source",
    header: "ソース",
    cell: ({ row }: { row: any }) => {
      const customer = row.original as CustomerProps;

      return (
        <Badge
          variant="outline"
          className={`text-xs ${getSourceColor(customer.source || TllCustomerSource.TLL_REFERAL)}`}
        >
          {getSourceLabel(customer.source || TllCustomerSource.TLL_REFERAL)}
        </Badge>
      );
    },
  },
  {
    accessorKey: "needs",
    header: "ニーズ数",
    cell: ({ row }: { row: any }) => {
      const customer = row.original as CustomerProps;
      const needsCount = customer.needs?.length || 0;

      if (needsCount === 0) {
        return <div className="text-sm text-neutral-400">-</div>;
      }

      return <Badge variant="outline">{needsCount}</Badge>;
    },
  },

  {
    accessorKey: "lastFollowUpDate",
    header: "最終フォローアップ",
    cell: ({ row }: { row: any }) => {
      const customer = row.original as any; // Extended CustomerProps with followUpRecords
      const followUpRecords = customer.followUpRecords || [];
      const totalRecords = followUpRecords.length;

      // Find the most recent follow-up record
      const lastFollowUp = followUpRecords.reduce(
        (latest: any, current: any) => {
          if (!latest) return current;
          return dayjs(current.followUpDate).isAfter(dayjs(latest.followUpDate))
            ? current
            : latest;
        },
        null,
      );

      if (!lastFollowUp) {
        return (
          <div className="text-sm text-neutral-500">
            <div>-</div>
            <div className="text-xs">記録数: {totalRecords}</div>
          </div>
        );
      }

      const followUpDate = dayjs(lastFollowUp.followUpDate);
      const daysAgo = dayjs().diff(followUpDate, "day");

      return (
        <div className="text-sm">
          <div>{followUpDate.format("YYYY/MM/DD")}</div>
          <div className="text-xs text-neutral-500">
            {daysAgo === 0 ? "今日" : `${daysAgo}日前`} | 担当:{" "}
            {lastFollowUp.agentUser?.name || "不明"}
          </div>
          <div className="text-xs text-blue-600 font-medium">
            記録数: {totalRecords}
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: "createdAt",
    header: "登録日",
    cell: ({ row }: { row: any }) => {
      const customer = row.original as any; // Extended CustomerProps with createdAt
      return (
        <div className="text-sm">
          {customer.createdAt
            ? dayjs(customer.createdAt).format("YYYY/MM/DD")
            : "-"}
        </div>
      );
    },
  },
  {
    id: "actions",
    header: "操作",
    cell: ({ row }: { row: any }) => {
      const customer = row.original as CustomerProps;
      return (
        <div className="flex flex-row gap-2">
          <Link href={`/pa/customer/${customer.id}/edit`}>
            <Button variant="outline" size="sm">
              <Pencil className="w-4 h-4" />
            </Button>
          </Link>
          <Link href={`/pa/customer/${customer.id}`}>
            <Button variant="outline" size="sm">
              <Eye className="w-4 h-4" />
            </Button>
          </Link>
          <FollowUpSheet
            customerId={customer.id || ""}
            customerName={customer.name || ""}
            onSuccess={onFollowUpSuccess}
          />
        </div>
      );
    },
  },
];

// Export default columns for backward compatibility
export const customerColumns = createCustomerColumns();
