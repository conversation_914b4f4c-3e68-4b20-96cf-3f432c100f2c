"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { TllUserProps } from "@/lib/definitions/tllUser";
import { getListOfAgentUserAction } from "@/actions/tllUserLambdaRecordFromSumifuAction";
import {
  Select,
  SelectValue,
  SelectContent,
  SelectTrigger,
  SelectItem,
} from "@/components/ui/select";
import { toast } from "@/hooks/use-toast";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { useAuthStore } from "@/store/auth";
import { TllCustomerMainNeedType, TllCustomerSource } from "@/lib/definitions";

interface CustomerFormData {
  name: string;
  email: string;
  phone: string;
  address: string;
  age: string;
  income: string;
  agentUserId: string;
  mainNeedType: string;
  source: string;
}

interface CustomerFormProps {
  initialData?: Partial<CustomerFormData>;
  onSubmit: (data: CustomerFormData) => Promise<void>;
  isLoading: boolean;
  submitButtonText: string;
  onCancel: () => void;
}

// Helper function to get display labels for enum values
const getMainNeedTypeLabel = (value: string) => {
  const labels: Record<string, string> = {
    [TllCustomerMainNeedType.RENTAL]: "賃貸",
    [TllCustomerMainNeedType.INVESTMENT_BUILDING]: "投資・一棟",
    [TllCustomerMainNeedType.INVESTMENT_UNIT]: "投資・区分",
    [TllCustomerMainNeedType.INVESTMENT_HOUSE]: "投資・戸建",
    [TllCustomerMainNeedType.OWNER_OCCUPIED_UNIT]: "実需・区分",
    [TllCustomerMainNeedType.OWNER_OCCUPIED_HOUSE]: "実需・戸建",
  };
  return labels[value] || value;
};

const getSourceLabel = (value: string) => {
  const labels: Record<string, string> = {
    [TllCustomerSource.URBALYTICS]: "Urbalytics",
    [TllCustomerSource.REFERRAL]: "知り合い・紹介",
    [TllCustomerSource.RAKUMACHI]: "楽待",
    [TllCustomerSource.KENBIYA]: "Kenbiya",
    [TllCustomerSource.ATHOME_SUUMO]: "Athome/Suumo",
    [TllCustomerSource.XIAOHONGSHU]: "小红书",
    [TllCustomerSource.WECHAT_GROUP]: "微信群",
  };
  return labels[value] || value;
};

export default function CustomerForm({
  initialData,
  onSubmit,
  isLoading,
  submitButtonText,
  onCancel,
}: CustomerFormProps) {
  const { currentUser } = useAuthStore();
  const [agentUsers, setAgentUsers] = useState<TllUserProps[]>([]);

  // Form state for all customer fields
  const [formData, setFormData] = useState<CustomerFormData>({
    name: initialData?.name || "",
    email: initialData?.email || "",
    phone: initialData?.phone || "",
    address: initialData?.address || "",
    age: initialData?.age || "",
    income: initialData?.income || "",
    agentUserId: initialData?.agentUserId || currentUser?.id || "",
    mainNeedType: initialData?.mainNeedType || "",
    source: initialData?.source || "",
  });

  useEffect(() => {
    getListOfAgentUserAction().then((res) => {
      if (res.success && res.data) {
        setAgentUsers(res.data);
      }
    });
  }, []);

  const handleSubmit = async () => {
    if (!formData.name.trim()) {
      toast({
        title: "入力エラー",
        description: "顧客名は必須です",
        variant: "destructive",
      });
      return;
    }

    await onSubmit(formData);
  };

  return (
    <div className="flex flex-col gap-4 p-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="flex flex-col gap-2">
          <Label htmlFor="name">顧客名 *</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) =>
              setFormData({ ...formData, name: e.target.value })
            }
            placeholder="顧客名を入力"
            required
          />
        </div>

        <div className="flex flex-col gap-2">
          <Label htmlFor="email">メールアドレス</Label>
          <Input
            id="email"
            type="email"
            value={formData.email}
            onChange={(e) =>
              setFormData({ ...formData, email: e.target.value })
            }
            placeholder="メールアドレスを入力"
          />
        </div>

        <div className="flex flex-col gap-2">
          <Label htmlFor="phone">電話番号</Label>
          <Input
            id="phone"
            value={formData.phone}
            onChange={(e) =>
              setFormData({ ...formData, phone: e.target.value })
            }
            placeholder="電話番号を入力"
          />
        </div>

        <div className="flex flex-col gap-2">
          <Label htmlFor="address">住所</Label>
          <Input
            id="address"
            value={formData.address}
            onChange={(e) =>
              setFormData({ ...formData, address: e.target.value })
            }
            placeholder="住所を入力"
          />
        </div>

        <div className="flex flex-col gap-2">
          <Label htmlFor="age">年齢</Label>
          <Input
            id="age"
            type="number"
            value={formData.age}
            onChange={(e) =>
              setFormData({ ...formData, age: e.target.value })
            }
            placeholder="年齢を入力"
          />
        </div>

        <div className="flex flex-col gap-2">
          <Label htmlFor="income">年収</Label>
          <Input
            id="income"
            type="number"
            value={formData.income}
            onChange={(e) =>
              setFormData({ ...formData, income: e.target.value })
            }
            placeholder="年収を入力"
          />
        </div>

        {/* Main Need Type */}
        <div className="flex flex-col gap-2">
          <Label>メインニーズタイプ</Label>
          <Select
            value={formData.mainNeedType}
            onValueChange={(value) => {
              setFormData({ ...formData, mainNeedType: value });
            }}
          >
            <SelectTrigger>
              <SelectValue placeholder="ニーズタイプを選択" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">未選択</SelectItem>
              {Object.values(TllCustomerMainNeedType).map((type) => (
                <SelectItem key={type} value={type}>
                  {getMainNeedTypeLabel(type)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Source */}
        <div className="flex flex-col gap-2">
          <Label>ソース</Label>
          <Select
            value={formData.source}
            onValueChange={(value) => {
              setFormData({ ...formData, source: value });
            }}
          >
            <SelectTrigger>
              <SelectValue placeholder="ソースを選択" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">未選択</SelectItem>
              {Object.values(TllCustomerSource).map((source) => (
                <SelectItem key={source} value={source}>
                  {getSourceLabel(source)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Agent Assignment */}
      <div className="flex flex-col gap-2">
        <Label>担当者</Label>
        {currentUser?.accessLevel && currentUser.accessLevel >= 90 ? (
          <Select
            value={formData.agentUserId}
            onValueChange={(value) => {
              setFormData({
                ...formData,
                agentUserId: value === "unassigned" ? "" : value,
              });
            }}
          >
            <SelectTrigger>
              <SelectValue placeholder="担当者を選択" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="unassigned">未割り当て</SelectItem>
              {agentUsers.map((user) => (
                <SelectItem key={user.id} value={user.id || ""}>
                  {user.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        ) : (
          <Input
            value={currentUser?.name || "未割り当て"}
            disabled
            className="bg-neutral-100"
          />
        )}
      </div>

      <div className="flex justify-start gap-2">
        <Button
          variant="outline"
          onClick={handleSubmit}
          disabled={isLoading}
        >
          {isLoading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
          {submitButtonText}
        </Button>
        <Button
          variant="ghost"
          onClick={onCancel}
          disabled={isLoading}
        >
          キャンセル
        </Button>
      </div>
    </div>
  );
}
