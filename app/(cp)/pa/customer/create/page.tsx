"use client";

import { Separator } from "@/components/ui/separator";
import { createCustomerAction } from "@/actions/customers";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { toast } from "@/hooks/use-toast";
import { useAuthStore } from "@/store/auth";
import { LARK_URLS, sendLark } from "@/lib/thirdParty/lark";
import CustomerForm from "../CustomerForm";

export default function CustomerCreatePage() {
  const router = useRouter();
  const { currentUser } = useAuthStore();
  const [isCreating, setIsCreating] = useState(false);

  const handleCreate = async (formData: any) => {
    setIsCreating(true);

    try {
      console.log("🔥 Creating customer with data:", formData);

      const createData = {
        name: formData.name,
        email: formData.email,
        phone: formData.phone,
        wechatAccount: formData.wechatAccount || undefined,
        address: formData.address,
        age: formData.age ? parseInt(formData.age) : undefined,
        income: formData.income ? parseInt(formData.income) : undefined,
        financialAsset: formData.financialAsset
          ? parseInt(formData.financialAsset)
          : undefined,
        propertyOwned: formData.propertyOwned || undefined,
        agentUserId: formData.agentUserId || null,
        language: formData.language || undefined,
        mainNeedType: formData.mainNeedType || undefined,
        source: formData.source || undefined,
      };

      const response = await createCustomerAction(createData);

      if (response.success) {
        toast({
          title: "作成完了",
          description: "顧客を作成しました",
          variant: "default",
        });

        await sendLark({
          message: `[⚙️][顧客作成][${currentUser?.name}は、${formData.name} さんを作成しました]`,
          url: LARK_URLS.USER_ACTIVITY_CHANNEL,
        });

        router.push(`/pa/customer`);
      } else {
        toast({
          title: "作成エラー",
          description: response.message || "顧客を作成できませんでした",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "作成エラー",
        description: "作成中にエラーが発生しました",
        variant: "destructive",
      });
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center p-4">
        <h1 className="text-2xl font-bold" aria-label="新規顧客登録">
          新規顧客登録
        </h1>
      </div>

      <Separator />

      <CustomerForm
        onSubmit={handleCreate}
        isLoading={isCreating}
        submitButtonText="作成"
        onCancel={() => router.push(`/pa/customer`)}
      />
    </div>
  );
}
