"use client";

import { Separator } from "@/components/ui/separator";
import { createCustomerAction } from "@/actions/customers";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { TllUserProps } from "@/lib/definitions/tllUser";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { getListOfAgentUserAction } from "@/actions/tllUserLambdaRecordFromSumifuAction";
import {
  Select,
  SelectValue,
  SelectContent,
  SelectTrigger,
  SelectItem,
} from "@/components/ui/select";
import { toast } from "@/hooks/use-toast";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { useAuthStore } from "@/store/auth";
import { LARK_URLS, sendLark } from "@/lib/thirdParty/lark";

export default function CustomerCreatePage() {
  const router = useRouter();
  const { currentUser } = useAuthStore();

  const [agentUsers, setAgentUsers] = useState<TllUserProps[]>([]);
  const [isCreating, setIsCreating] = useState(false);

  // Form state for all customer fields
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    address: "",
    age: "",
    income: "",
    agentUserId: currentUser?.id || "", // Default to logged-in user
  });

  useEffect(() => {
    getListOfAgentUserAction().then((res) => {
      if (res.success && res.data) {
        setAgentUsers(res.data);
      }
    });
  }, []);

  const handleCreate = async () => {
    if (!formData.name.trim()) {
      toast({
        title: "入力エラー",
        description: "顧客名は必須です",
        variant: "destructive",
      });
      return;
    }

    setIsCreating(true);

    try {
      console.log("🔥 Creating customer with data:", formData);

      const createData = {
        name: formData.name,
        email: formData.email,
        phone: formData.phone,
        address: formData.address,
        age: formData.age ? parseInt(formData.age) : undefined,
        income: formData.income ? parseInt(formData.income) : undefined,
        agentUserId: formData.agentUserId || null,
      };

      const response = await createCustomerAction(createData);

      if (response.success) {
        toast({
          title: "作成完了",
          description: "顧客を作成しました",
          variant: "default",
        });

        await sendLark({
          message: `[⚙️][顧客作成][${currentUser?.name}は、${formData.name} さんを作成しました]`,
          url: LARK_URLS.USER_ACTIVITY_CHANNEL,
        });

        router.push(`/pa/customer`);
      } else {
        toast({
          title: "作成エラー",
          description: response.message || "顧客を作成できませんでした",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "作成エラー",
        description: "作成中にエラーが発生しました",
        variant: "destructive",
      });
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center p-4">
        <h1 className="text-2xl font-bold" aria-label="新規顧客登録">
          新規顧客登録
        </h1>
      </div>

      <Separator className="" />

      <div className="flex flex-col gap-4 p-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex flex-col gap-2">
            <Label htmlFor="name">顧客名 *</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) =>
                setFormData({ ...formData, name: e.target.value })
              }
              placeholder="顧客名を入力"
              required
            />
          </div>

          <div className="flex flex-col gap-2">
            <Label htmlFor="email">メールアドレス</Label>
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) =>
                setFormData({ ...formData, email: e.target.value })
              }
              placeholder="メールアドレスを入力"
            />
          </div>

          <div className="flex flex-col gap-2">
            <Label htmlFor="phone">電話番号</Label>
            <Input
              id="phone"
              value={formData.phone}
              onChange={(e) =>
                setFormData({ ...formData, phone: e.target.value })
              }
              placeholder="電話番号を入力"
            />
          </div>

          <div className="flex flex-col gap-2">
            <Label htmlFor="address">住所</Label>
            <Input
              id="address"
              value={formData.address}
              onChange={(e) =>
                setFormData({ ...formData, address: e.target.value })
              }
              placeholder="住所を入力"
            />
          </div>

          <div className="flex flex-col gap-2">
            <Label htmlFor="age">年齢</Label>
            <Input
              id="age"
              type="number"
              value={formData.age}
              onChange={(e) =>
                setFormData({ ...formData, age: e.target.value })
              }
              placeholder="年齢を入力"
            />
          </div>

          <div className="flex flex-col gap-2">
            <Label htmlFor="income">年収</Label>
            <Input
              id="income"
              type="number"
              value={formData.income}
              onChange={(e) =>
                setFormData({ ...formData, income: e.target.value })
              }
              placeholder="年収を入力"
            />
          </div>
        </div>

        {/* Agent Assignment - Default to logged-in user */}
        <div className="flex flex-col gap-2">
          <Label>担当者</Label>
          <Select
  
            value={formData.agentUserId}
            onValueChange={(value) => {
              setFormData({
                ...formData,
                agentUserId: value === "unassigned" ? "" : value,
              });
            }}
          >
            <SelectTrigger>
              <SelectValue placeholder="担当者を選択" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="unassigned">未割り当て</SelectItem>
              {agentUsers.map((user) => (
                <SelectItem key={user.id} value={user.id || ""}>
                  {user.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="flex justify-start gap-2">
          <Button
            variant="outline"
            onClick={handleCreate}
            disabled={isCreating}
          >
            {isCreating && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
            作成
          </Button>
          <Button
            variant="ghost"
            onClick={() => router.push(`/pa/customer`)}
            disabled={isCreating}
          >
            キャンセル
          </Button>
        </div>
      </div>
    </div>
  );
}
