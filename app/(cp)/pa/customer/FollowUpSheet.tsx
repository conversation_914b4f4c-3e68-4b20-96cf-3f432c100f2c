"use client";

import { useState, useEffect } from "react";
import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetDes<PERSON>,
  She<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { FileText } from "lucide-react";
import { DataTable } from "@/components/ui/data-table";
import { CustomerFollowUpRecordProps } from "@/lib/definitions";
import { fetchFollowUpRecordsAction } from "@/actions/customerFollowUpRecords";
import { followUpColumns } from "./followUpColumns";

interface FollowUpSheetProps {
  customerId: string;
  customerName: string;
}

export default function FollowUpSheet({ customerId, customerName }: FollowUpSheetProps) {
  const [followUpRecords, setFollowUpRecords] = useState<CustomerFollowUpRecordProps[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);

  const fetchFollowUpRecords = async () => {
    setIsLoading(true);
    try {
      const response = await fetchFollowUpRecordsAction();
      if (response.success) {
        // Filter records for this specific customer
        const customerRecords = (response.data as CustomerFollowUpRecordProps[]).filter(
          (record) => record.customerId === customerId
        );
        setFollowUpRecords(customerRecords);
      }
    } catch (error) {
      console.error("🔥 フォローアップ記録取得エラー:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      fetchFollowUpRecords();
    }
  }, [isOpen, customerId]);

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        <Button variant="outline" size="sm" title="フォローアップ記録">
          <FileText className="w-4 h-4" />
        </Button>
      </SheetTrigger>
      <SheetContent className="w-[800px] sm:max-w-[800px]">
        <SheetHeader>
          <SheetTitle>フォローアップ記録</SheetTitle>
          <SheetDescription>
            {customerName} さんのフォローアップ記録一覧
          </SheetDescription>
        </SheetHeader>
        
        <div className="mt-6">
          {isLoading ? (
            <div className="flex justify-center items-center h-32">
              <div className="text-sm text-neutral-500">読み込み中...</div>
            </div>
          ) : (
            <DataTable
              data={followUpRecords}
              columns={followUpColumns}
              searchColumn="comments"
              showFooter={false}
            />
          )}
        </div>
      </SheetContent>
    </Sheet>
  );
}
