"use client";

import { useState, useEffect } from "react";
import {
  Sheet,
  SheetContent,
  SheetDes<PERSON>,
  Sheet<PERSON>eader,
  Sheet<PERSON><PERSON><PERSON>,
  SheetTrigger,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { FileText } from "lucide-react";
import { DataTable } from "@/components/ui/data-table";
import { CustomerFollowUpRecordProps } from "@/lib/definitions";
import {
  fetchFollowUpRecordsAction,
  createFollowUpRecordAction,
} from "@/actions/customerFollowUpRecords";
import { followUpColumns } from "./followUpColumns";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Plus, Loader2 } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  Di<PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";

interface FollowUpSheetProps {
  customerId: string;
  customerName: string;
  onSuccess?: () => void;
}

export default function FollowUpSheet({
  customerId,
  customerName,
  onSuccess,
}: FollowUpSheetProps) {
  const [followUpRecords, setFollowUpRecords] = useState<
    CustomerFollowUpRecordProps[]
  >([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isCreating, setIsCreating] = useState(false);

  // Set default date to today
  const today = new Date().toISOString().split("T")[0];

  const [formData, setFormData] = useState({
    followUpDate: today,
    comments: "",
  });

  const fetchFollowUpRecords = async () => {
    setIsLoading(true);
    try {
      const response = await fetchFollowUpRecordsAction();
      if (response.success) {
        // Filter records for this specific customer
        const customerRecords = (
          response.data as CustomerFollowUpRecordProps[]
        ).filter((record) => record.customerId === customerId);
        setFollowUpRecords(customerRecords);
      }
    } catch (error) {
      console.error("🔥 フォローアップ記録取得エラー:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateFollowUp = async () => {
    if (!formData.followUpDate) {
      toast({
        title: "入力エラー",
        description: "フォローアップ日は必須です",
        variant: "destructive",
      });
      return;
    }

    setIsCreating(true);

    try {
      const followUpData: CustomerFollowUpRecordProps = {
        customerId: customerId,
        followUpDate: new Date(formData.followUpDate),
        comments: formData.comments,
        agentUserId: "", // This will be set by the server action
      };

      const result = await createFollowUpRecordAction(followUpData);

      if (result.success) {
        toast({
          title: "作成完了",
          description: "フォローアップ記録を作成しました",
        });

        // Reset form
        setFormData({
          followUpDate: today,
          comments: "",
        });

        setIsDialogOpen(false);

        // Refresh the records
        fetchFollowUpRecords();

        // Call parent callback to refresh customer data
        if (onSuccess) {
          onSuccess();
        }
      } else {
        toast({
          title: "作成エラー",
          description: result.message || "作成に失敗しました",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "作成エラー",
        description: "作成中にエラーが発生しました",
        variant: "destructive",
      });
    } finally {
      setIsCreating(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      fetchFollowUpRecords();
    }
  }, [isOpen, customerId]);

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        <Button variant="outline" size="sm" title="フォローアップ記録">
          <FileText className="w-4 h-4" />
        </Button>
      </SheetTrigger>
      <SheetContent className="w-[800px] sm:max-w-[800px]">
        <SheetHeader>
          <SheetTitle>フォローアップ記録</SheetTitle>
          <SheetDescription>
            {customerName} さんのフォローアップ記録一覧
          </SheetDescription>
        </SheetHeader>

        <div className="mt-6">
          <div className="mb-4 flex justify-between items-center">
            <h3 className="text-lg font-semibold">記録一覧</h3>
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm">
                  <Plus className="w-4 h-4 mr-2" />
                  新規記録追加
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>新規フォローアップ記録</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="flex flex-col gap-2">
                    <Label htmlFor="followUpDate">フォローアップ日</Label>
                    <Input
                      id="followUpDate"
                      type="date"
                      value={formData.followUpDate}
                      onChange={(e) =>
                        setFormData({
                          ...formData,
                          followUpDate: e.target.value,
                        })
                      }
                    />
                  </div>
                  <div className="flex flex-col gap-2">
                    <Label htmlFor="comments">コメント</Label>
                    <Textarea
                      id="comments"
                      value={formData.comments}
                      onChange={(e) =>
                        setFormData({ ...formData, comments: e.target.value })
                      }
                      placeholder="フォローアップの内容を入力してください"
                      rows={4}
                    />
                  </div>
                  <div className="flex justify-end gap-2">
                    <Button
                      variant="outline"
                      onClick={() => setIsDialogOpen(false)}
                      disabled={isCreating}
                    >
                      キャンセル
                    </Button>
                    <Button
                      onClick={handleCreateFollowUp}
                      disabled={isCreating}
                    >
                      {isCreating && (
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      )}
                      作成
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
          {isLoading ? (
            <div className="flex justify-center items-center h-32">
              <div className="text-sm text-neutral-500">読み込み中...</div>
            </div>
          ) : (
            <DataTable
              data={followUpRecords}
              columns={followUpColumns}
              searchColumn="comments"
              showFooter={false}
            />
          )}
        </div>
      </SheetContent>
    </Sheet>
  );
}
