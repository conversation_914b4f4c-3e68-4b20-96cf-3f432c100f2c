"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { CustomerProps, CustomerFollowUpRecordProps } from "@/lib/definitions";
import { createFollowUpRecordAction } from "@/actions/customerFollowUpRecords";
import { fetchCustomersAction } from "@/actions/customers";
import { useAuthStore } from "@/store/auth";
import { toast } from "@/hooks/use-toast";
import { Plus, Loader2 } from "lucide-react";

interface FollowUpRecordFormProps {
  onSuccess?: () => void;
}

export default function FollowUpRecordForm({
  onSuccess,
}: FollowUpRecordFormProps) {
  const [open, setOpen] = useState(false);
  const [customers, setCustomers] = useState<CustomerProps[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { currentUser } = useAuthStore();

  // 🔥 Set default date to today
  const today = new Date().toISOString().split("T")[0];

  const [formData, setFormData] = useState({
    customerId: "",
    followUpDate: today,
    comments: "",
  });

  useEffect(() => {
    if (open) {
      fetchCustomers();
    }
  }, [open]);

  const fetchCustomers = async () => {
    try {
      const response = await fetchCustomersAction();
      if (response.success) {
        // 🔥 Filter customers to only show those assigned to current user
        const myCustomers = (response.data as CustomerProps[]).filter(
          (customer) => customer.agentUserId === currentUser?.id,
        );
        setCustomers(myCustomers);
      }
    } catch (error) {
      console.error("🔥 顧客データ取得エラー:", error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.customerId || !formData.followUpDate) {
      toast({
        title: "入力エラー",
        description: "顧客とフォローアップ日は必須です",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    try {
      const followUpData: CustomerFollowUpRecordProps = {
        customerId: formData.customerId,
        followUpDate: new Date(formData.followUpDate),
        comments: formData.comments,
        agentUserId: "", // This will be set by the server action
      };

      const result = await createFollowUpRecordAction(followUpData);

      if (result.success) {
        toast({
          title: "作成完了",
          description: "フォローアップ記録を作成しました",
        });

        // Reset form with default date
        setFormData({
          customerId: "",
          followUpDate: today,
          comments: "",
        });

        setOpen(false);

        if (onSuccess) {
          onSuccess();
        }
      } else {
        toast({
          title: "作成エラー",
          description: result.message || "作成に失敗しました",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "作成エラー",
        description: "作成中にエラーが発生しました",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline">
          <Plus className="w-4 h-4 mr-2" />
          新規フォローアップ記録
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>フォローアップ記録作成</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="customer">顧客 *</Label>
            <Select
              value={formData.customerId}
              onValueChange={(value) =>
                setFormData({ ...formData, customerId: value })
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="顧客を選択してください" />
              </SelectTrigger>
              <SelectContent>
                {customers.map((customer) => (
                  <SelectItem key={customer.id} value={customer.id || ""}>
                    {customer.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="followUpDate">フォローアップ日 *</Label>
            <Input
              id="followUpDate"
              type="date"
              value={formData.followUpDate}
              onChange={(e) =>
                setFormData({ ...formData, followUpDate: e.target.value })
              }
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="comments">コメント</Label>
            <Textarea
              id="comments"
              placeholder="紹介した内容、顧客からのフィードバック、次回のアクション等を記入してください"
              value={formData.comments}
              onChange={(e) =>
                setFormData({ ...formData, comments: e.target.value })
              }
              rows={4}
            />
          </div>

          <div className="flex justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={isLoading}
            >
              キャンセル
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
              作成
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
