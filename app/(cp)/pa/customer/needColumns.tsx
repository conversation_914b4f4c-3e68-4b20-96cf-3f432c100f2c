import { deleteCustomerNeedAction } from "@/actions/customerNeeds";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { toast } from "@/hooks/use-toast";
import dayjs from "dayjs";
import { <PERSON>, <PERSON>cil, Trash, <PERSON>, Eye } from "lucide-react";
import Link from "next/link";
import { Row } from "react-day-picker";
import { sendLark, LARK_URLS } from "@/lib/thirdParty/lark";
import { useAuthStore } from "@/store/auth";
import {
  CustomerNeedProps,
  TllCustomerNeedCriteriaType,
} from "@/lib/definitions";
import { Badge } from "@/components/ui/badge";
import { mapper } from "../../an/(common)/recordTypeMapper";
import { UserLambdaRecordType } from "@/lib/definitions/userLambdaRecord";
import { isArray } from "util";
import { useCustomerNeedStore } from "@/store/customerNeed";

export const padNeedsWithMatch = (
  match: {
    [key: string]: {
      matchCount: number;
      need: CustomerNeedProps;
    };
  },
  needColumns: any[],
) => {
  return [
    {
      header: "物件数",
      cell: ({ row }: { row: any }) => {
        return match && row.original.id && match[row.original.id] ? (
          <Link
            href={`/ex/need/${row.original.id}`}
            className="underline text-green-500 hover:text-green-600 font-bold text-sm"
          >
            {match[row.original.id].matchCount}
          </Link>
        ) : (
          <div className="text-center text-neutral-200">-</div>
        );
      },
    },
    ...needColumns,
  ];
};

export const needColumns = [
  // {
  //   header: "ID",
  //   cell: ({ row }: { row: any }) => {
  //     return <Link href={`/ex/need/${row.original?.id}`} className="underline">#{row.index + 1}</Link>;
  //   },
  // },
  // { header: "客户用户ID", accessorKey: "customerUserId" },
  // { header: "優先度", accessorKey: "priority" },
  {
    header: "タイトル ",
    cell: ({ row }: { row: any }) => {
      return (
        <div>
          <div className="text-sm text-neutral-500">
            {row.original.title ? row.original.title : "-"}
          </div>
          {row.original.comment && (
            <div className="text-xs text-neutral-500">
              {row.original.comment}
            </div>
          )}
        </div>
      );
    },
  },
  {
    header: "タイプ",
    cell: ({ row }: { row: any }) => {
      return (
        <div>
          {mapper[row.original.recordType as UserLambdaRecordType]?.name}
        </div>
      );
    },
  },
  {
    header: "価格代(万円)",
    cell: ({ row }: { row: any }) => {
      return (
        <div>
          {row.original.priceFrom ? `${row.original.priceFrom}` : ""}~
          {row.original.priceTo ? `${row.original.priceTo}` : ""}
        </div>
      );
    },
  },
  {
    header: "ROI(%)",
    cell: ({ row }: { row: any }) => {
      return (
        <div>{row.original.roiFrom ? `${row.original.roiFrom}%` : ""}</div>
      );
    },
  },
  {
    header: "条件タイプ",
    cell: ({ row }: { row: any }) => {
      return (
        <div className="flex flex-row justify-center gap-2 text-xs text-center">
          {row.original.criteriaType ===
            TllCustomerNeedCriteriaType.AREA_CODE && (
            <Badge variant="outline" className="flex flex-row gap-2">
              エリア
            </Badge>
          )}
          {row.original.criteriaType ===
            TllCustomerNeedCriteriaType.POSTAL_CODE && (
            <Badge variant="outline" className="flex flex-row gap-2">
              郵便番号
            </Badge>
          )}
          {row.original.criteriaType ===
            TllCustomerNeedCriteriaType.STATION_GROUP_ID && (
            <Badge variant="outline" className="flex flex-row gap-2">
              最寄駅
            </Badge>
          )}
          {row.original.criteriaType ===
            TllCustomerNeedCriteriaType.BUILDING_ID && (
            <Badge variant="outline" className="flex flex-row gap-2">
              建物名
            </Badge>
          )}
        </div>
      );
    },
  }, // 使用日语
  {
    header: "条件",
    cell: ({ row }: { row: any }) => {
      return (
        <div className="text-xs flex flex-row flex-wrap gap-2 w-[240px] text-left text-ellipsis">
          {row.original.paddedWatchedValues?.map((value: any, i: number) => (
            <span key={i} className="break-all">
              {value.label}
            </span>
          ))}
        </div>
      );
    },
  }, // 使用日语
  { header: "最寄駅徒歩", accessorKey: "nearestStationWalkMinuteTo" }, // 使用日语
  {
    header: "土地面積",
    cell: ({ row }: { row: any }) => {
      return (
        <div>
          {row.original.landAreaFrom ? `${row.original.landAreaFrom}` : ""}~
          {row.original.landAreaTo ? `${row.original.landAreaTo}` : ""}
        </div>
      );
    },
  }, // 使用日语

  {
    header: "建物面積",
    accessorKey: "buildingAreaFrom",
    cell: ({ row }: { row: any }) => {
      return (
        <div>
          {row.original.buildingAreaFrom
            ? `${row.original.buildingAreaFrom}`
            : ""}
          ~{row.original.buildingAreaTo ? `${row.original.buildingAreaTo}` : ""}
        </div>
      );
    },
  }, // 使用日语
  {
    header: "建築年",
    cell: ({ row }: { row: any }) => {
      return (
        <div>
          {row.original.yearFrom ? `${row.original.yearFrom}` : ""}~
          {row.original.yearTo ? `${row.original.yearTo}` : ""}
        </div>
      );
    },
  },
  {
    header: "ホテル可",
    cell: ({ row }: { row: any }) => {
      return (
        <div className="flex items-center justify-center">
          {row.original.landCanHotel === 1 ? (
            <Check className="w-4 h-4 text-green-500" />
          ) : (
            <X className="w-4 h-4 text-neutral-500" />
          )}
        </div>
      );
    },
  }, // 使用日语
  // { header: "建材", accessorKey: "buildingMaterial" }, // 使用日语

  // { header: "フォロー中", accessorKey: "isFollow" }, // 使用日语
  // { header: "コメント", accessorKey: "comment" }, // 使用日语
  {
    header: "作成日",
    cell: ({ row }: { row: any }) => {
      return <div>{dayjs(row.original.createdAt).format("YYYY-MM-DD")}</div>;
    },
  }, // 使用日
  {
    header: "操作",
    cell: ({ row }: { row: any }) => {
      const { currentUser } = useAuthStore();
      const { removeNeed } = useCustomerNeedStore((state) => state);

      return (
        <div>
          <Link href={`/ex/need/${row.original.id}`} className="mr-2">
            <Button variant="outline" size="icon">
              <Eye className="w-4 h-4" />
            </Button>
          </Link>
          <Link href={`/ex/need/${row.original.id}/edit`} className="mr-2">
            <Button variant="outline" size="icon">
              <Pencil className="w-4 h-4" />
            </Button>
          </Link>
          <Button
            variant="destructive"
            size="icon"
            onClick={async () => {
              let res = await deleteCustomerNeedAction(row.original.id);
              if (res.success) {
                toast({
                  title: "更新成功",
                  description: "ニーズが更新されました",
                });
                await sendLark({
                  message: `[⚙️][顧客ニーズ削除][${currentUser?.name}は、${row.original.customer?.name} さんのニーズを削除しました]`,
                  url: LARK_URLS.USER_ACTIVITY_CHANNEL,
                });

                await removeNeed(row.original.id);
                await new Promise((resolve) => setTimeout(resolve, 1000));
                window.location.reload();
              }
            }}
          >
            <Trash className="w-4 h-4" />
          </Button>
        </div>
      );
    },
  },
];
