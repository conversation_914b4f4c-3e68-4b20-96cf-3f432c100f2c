"use client";

import { useEffect, useState } from "react";
import { DataTable } from "@/components/ui/data-table";
import {
  CustomerNeedProps,
  CustomerProps,
  CustomerFollowUpRecordProps,
} from "@/lib/definitions";
import { fetchCustomersAction } from "@/actions/customers";
import { fetchFollowUpRecordsAction } from "@/actions/customerFollowUpRecords";
import { needColumns, padNeedsWithMatch } from "./needColumns";
import { customerColumns } from "./customerColumns";
import { followUpColumns } from "./followUpColumns";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { useAuthStore } from "@/store/auth";

import { getRecordMatchSimpleForNeedIdsAction } from "@/actions/customerNeeds";
import Link from "next/link";
import FollowUpRecordForm from "./FollowUpRecordForm";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export default function CustomerPage() {
  const [customers, setCustomers] = useState<CustomerProps[]>([]);
  const [followUpRecords, setFollowUpRecords] = useState<
    CustomerFollowUpRecordProps[]
  >([]);
  const { currentUser } = useAuthStore();
  const [selectedAgent, setSelectedAgent] = useState<string | null>(null);
  const [paddedColumns, setPaddedColumns] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const router = useRouter();
  const searchParams = useSearchParams();

  // 🔥 Get active tab from URL query params
  const activeTab = searchParams.get("tab") || "unassigned";

  // 🔥 Handle tab change with query parameters
  const handleTabChange = (newTab: string) => {
    const params = new URLSearchParams(searchParams);
    params.set("tab", newTab);
    router.push(`/pa/customer?${params.toString()}`);
  };

  const fetchCustomers = async () => {
    const response = await fetchCustomersAction();
    if (response.success) {
      // Fetch follow-up records and merge with customers
      const followUpResponse = await fetchFollowUpRecordsAction();
      const followUpRecords = followUpResponse.success
        ? followUpResponse.data
        : [];

      // Add follow-up records to each customer
      const customersWithFollowUps = response.data.map((customer: any) => ({
        ...customer,
        followUpRecords: followUpRecords.filter(
          (record: any) => record.customerId === customer.id,
        ),
      }));

      setCustomers(customersWithFollowUps);
    }
  };

  const fetchFollowUpRecords = async () => {
    const response = await fetchFollowUpRecordsAction();
    if (response.success) {
      setFollowUpRecords(response.data as CustomerFollowUpRecordProps[]);
    }
  };

  const fetchCustomerAndNeedMatches = async () => {
    setIsLoading(true);
    const res = await getRecordMatchSimpleForNeedIdsAction({
      needIds: customers
        .filter(
          (customer: CustomerProps) =>
            customer.agentUser?.name === selectedAgent,
        )
        .map((customer: CustomerProps) =>
          customer.needs?.map((need: CustomerNeedProps) => need.id),
        )
        .flat() as string[],
    });

    setPaddedColumns(padNeedsWithMatch(res.data, needColumns));
    setIsLoading(false);
  };

  // No longer needed - query params handle URL state automatically

  useEffect(() => {
    if (currentUser) {
      fetchCustomers();
      fetchFollowUpRecords();
      setSelectedAgent(currentUser?.name || null);
    }
  }, [currentUser]);

  useEffect(() => {
    if (selectedAgent && customers.length > 0) {
      fetchCustomerAndNeedMatches();
    }
  }, [selectedAgent, customers]);

  const distinctAgents = Array.from(
    new Set(
      customers.map((customer: CustomerProps) => customer.agentUser?.name),
    ),
  ).filter(Boolean);

  // Calculate customer counts
  const unassignedCustomers = customers.filter(
    (customer: CustomerProps) => !customer.agentUserId,
  );
  const totalCustomers = customers.length;

  return (
    <div className="">
      <div className="p-4 flex flex-row gap-4 justify-between items-center border-b border-neutral-200">
        <h1 className="text-2xl font-bold">顧客管理</h1>

        <div className="flex flex-row gap-4">
          <Button
            variant="outline"
            onClick={() => {
              router.push("/pa/customer/create");
            }}
          >
            新規顧客登録
          </Button>
        </div>
      </div>

      <Tabs
        value={activeTab}
        onValueChange={handleTabChange}
        className="w-full"
      >
        <div className="p-4 border-b border-neutral-200">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="unassigned" className="flex items-center gap-2">
              未割り当て顧客
              <Badge variant="secondary">{unassignedCustomers.length}</Badge>
            </TabsTrigger>
            <TabsTrigger value="customers" className="flex items-center gap-2">
              顧客リスト
              <Badge variant="secondary">{totalCustomers}</Badge>
            </TabsTrigger>
            <TabsTrigger value="needs">ニーズリスト</TabsTrigger>
            <TabsTrigger value="followups">フォローアップ記録</TabsTrigger>
          </TabsList>
        </div>

        {/* Unassigned Customers Tab */}
        <TabsContent value="unassigned" className="p-4">
          <div className="mb-4">
            <h2 className="text-lg font-semibold">未割り当て顧客</h2>
            <p className="text-sm text-neutral-600">
              担当者が割り当てられていない顧客一覧
            </p>
          </div>

          <DataTable
            data={unassignedCustomers}
            columns={customerColumns}
            searchColumn="name"
            showFooter={false}
          />
        </TabsContent>

        {/* Customer List Tab */}
        <TabsContent value="customers" className="p-4">
          {currentUser?.accessLevel && currentUser?.accessLevel >= 90 && (
            <div className="mb-4 flex items-center gap-2">
              <div className="flex flex-col">
                <label className="text-sm font-medium mb-1">担当者</label>
                <Select
                  value={selectedAgent || "ALL"}
                  onValueChange={(value) => {
                    setSelectedAgent(value === "ALL" ? null : value);
                  }}
                >
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="担当者を選択" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ALL">
                      すべて ({customers.length})
                    </SelectItem>
                    {distinctAgents.map((agentName, index) => (
                      <SelectItem key={index} value={agentName}>
                        {agentName} (
                        {
                          customers.filter(
                            (customer: CustomerProps) =>
                              customer.agentUser?.name === agentName,
                          ).length
                        }
                        )
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}

          <DataTable
            data={customers.filter((customer: CustomerProps) => {
              if (selectedAgent) {
                if (selectedAgent === "未割り当て") {
                  return !customer.agentUserId;
                }
                return customer.agentUser?.name === selectedAgent;
              }
              // 🔥 Default: show customers assigned to me + unassigned customers
              if (currentUser?.accessLevel && currentUser?.accessLevel >= 90) {
                return true; // Admin sees all
              }
              return (
                customer.agentUserId === currentUser?.id ||
                !customer.agentUserId
              );
            })}
            columns={customerColumns}
            searchColumn="name"
            showFooter={false}
          />
        </TabsContent>

        {/* Needs List Tab - Original Customer Needs Display */}
        <TabsContent value="needs" className="p-4">
          <div className="mb-4 flex justify-between items-center">
            <h2 className="text-lg font-semibold">顧客ニーズリスト</h2>
            <Link href="/ex/need/new">
              <Button variant="outline">
                <Plus className="w-4 h-4 mr-2" />
                新規ニーズ作成
              </Button>
            </Link>
          </div>

          {currentUser?.accessLevel && currentUser?.accessLevel >= 90 && (
            <div className="mb-4 flex items-center gap-2">
              <label className="text-sm font-medium">担当者:</label>
              <Select
                value={selectedAgent || "ALL"}
                onValueChange={(value) => {
                  setSelectedAgent(value === "ALL" ? null : value);
                }}
              >
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="担当者を選択" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">
                    すべて ({customers.length})
                  </SelectItem>
                  <SelectItem value="未割り当て">
                    未割り当て (
                    {
                      customers.filter(
                        (customer: CustomerProps) => !customer.agentUserId,
                      ).length
                    }
                    )
                  </SelectItem>
                  {distinctAgents.map((agentName, index) => (
                    <SelectItem key={index} value={agentName}>
                      {agentName} (
                      {
                        customers.filter(
                          (customer: CustomerProps) =>
                            customer.agentUser?.name === agentName,
                        ).length
                      }
                      )
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          <div className="flex flex-col gap-4">
            {customers.length ? (
              customers
                .filter((customer: CustomerProps) => {
                  if (selectedAgent) {
                    if (selectedAgent === "未割り当て") {
                      return !customer.agentUserId;
                    }
                    return customer.agentUser?.name === selectedAgent;
                  }
                  // 🔥 Default: show customers assigned to me + unassigned customers
                  if (
                    currentUser?.accessLevel &&
                    currentUser?.accessLevel >= 90
                  ) {
                    return true; // Admin sees all
                  }
                  return (
                    customer.agentUserId === currentUser?.id ||
                    !customer.agentUserId
                  );
                })
                .map((customer: CustomerProps) => (
                  <div
                    key={customer.id}
                    className="bg-neutral-50 p-4 rounded-md"
                  >
                    <div className="flex flex-row gap-4 justify-between items-center mb-2">
                      <div className="flex flex-col">
                        <div className="text-lg font-bold">{customer.name}</div>
                        <div className="flex flex-row gap-4">
                          <div className="text-sm text-neutral-500">
                            担当: {customer?.agentUser?.name}
                          </div>
                          <div className="text-sm text-neutral-500">
                            住所: {customer?.address || "-"}
                          </div>
                          <div className="text-sm text-neutral-500">
                            年齢: {customer?.age ? `${customer.age}歳` : "-"}
                          </div>
                          <div className="text-sm text-neutral-500">
                            収入: {customer?.income || "-"}
                          </div>
                        </div>
                      </div>
                    </div>

                    <div>
                      {customer.needs && customer.needs.length > 0 ? (
                        <DataTable
                          data={customer.needs}
                          columns={
                            paddedColumns.length > 0
                              ? paddedColumns
                              : needColumns
                          }
                          searchColumn="title"
                          showFooter={false}
                          isLoading={isLoading}
                        />
                      ) : (
                        <div className="text-neutral-500">
                          ニーズがありません
                        </div>
                      )}
                    </div>
                  </div>
                ))
            ) : (
              <div className="text-neutral-500">顧客が存在しません</div>
            )}
          </div>
        </TabsContent>

        {/* Follow-up Records Tab */}
        <TabsContent value="followups" className="p-4">
          <div className="mb-4 flex justify-between items-center">
            <h2 className="text-lg font-semibold">フォローアップ記録</h2>
            <FollowUpRecordForm
              onSuccess={() => {
                fetchFollowUpRecords();
              }}
            />
          </div>

          <DataTable
            data={followUpRecords}
            columns={followUpColumns}
            searchColumn="customer.name"
            showFooter={false}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
