"use client";

import { Separator } from "@/components/ui/separator";
import { useParams } from "next/navigation";
import {
  fetchCustomersAction,
  updateCustomerAction,
} from "@/actions/customers";
import { DataTable } from "@/components/ui/data-table";
import { But<PERSON> } from "@/components/ui/button";
import { Loader2, Pencil } from "lucide-react";
import { CustomerProps } from "@/lib/definitions";
import { TllUserProps } from "@/lib/definitions/tllUser";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { getListOfAgentUserAction } from "@/actions/tllUserLambdaRecordFromSumifuAction";
import {
  Select,
  SelectValue,
  SelectContent,
  SelectTrigger,
  SelectItem,
} from "@/components/ui/select";
import { toast } from "@/hooks/use-toast";
import { needColumns } from "../needColumns";
import {
  Card,
  CardContent,
  CardT<PERSON>le,
  CardHeader,
  CardDescription,
} from "@/components/ui/card";
import Link from "next/link";

export default function CustomerEditPage() {
  const { id } = useParams();
  const router = useRouter();

  const [customer, setCustomer] = useState<CustomerProps | null>(null);
  const [agentUsers, setAgentUsers] = useState<TllUserProps[]>([]);
  const [isLoadingCustomer, setIsLoadingCustomer] = useState(false);
  const [selectedAgentId, setSelectedAgentId] = useState<string | null>(null);

  const fetchCustomer = async () => {
    setIsLoadingCustomer(true);
    const response = await fetchCustomersAction(id as string);
    if (response.success && response.data) {
      setCustomer(response.data[0]);
      setSelectedAgentId(response.data[0].agentUserId || null);
    }
    setIsLoadingCustomer(false);
  };

  useEffect(() => {
    getListOfAgentUserAction().then((res) => {
      if (res.success && res.data) {
        setAgentUsers(res.data);
      }
    });

    if (id) {
      fetchCustomer();
    }
  }, [id]);

  const handleUpdate = async () => {
    console.log("update");
    if (!selectedAgentId) {
      toast({
        title: "担当者を選択してください",
        description: "担当者を選択してください",
        variant: "destructive",
      });
      return;
    }
    const response = await updateCustomerAction({
      id: customer?.id,
      agentUserId: selectedAgentId,
      // name: customer?.name || "",
      // id: customer?.id || "",
      // language: customer?.language || "",
      // email: customer?.email || "",
      // phone: customer?.phone || "",
      // address: customer?.address || "",
      // age: customer?.age || "",
      // income: customer?.income || "",
    });
    if (response.success) {
      toast({
        title: "担当者を更新しました",
        description: "担当者を更新しました",
        variant: "default",
      });
    } else {
      toast({
        title: "担当者を更新できませんでした",
        description: "担当者を更新できませんでした",
        variant: "destructive",
      });
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center p-4">
        <h1 className="text-2xl font-bold" aria-label="顧客情報">
          顧客情報: {customer?.name}
        </h1>
      </div>

      <Separator className="" />

      <div className="p-4 flex flex-col gap-4">
        <div className="flex flex-col bg-neutral-50 rounded-md">
          <div className="flex flex-row gap-2 items-center justify-between border-b border-neutral-200 pb-2 px-4 pt-2">
            <div className="text-lg font-bold">顧客情報</div>
            <Link
              href={`/pa/customer/${id}/edit`}
              className="flex flex-row gap-4"
            >
              <Button variant="outline">データを編集</Button>
            </Link>
          </div>
          <div className="p-4">
            {!customer ? (
              <div>
                <Loader2 className="w-4 h-4 animate-spin" />
              </div>
            ) : (
              <div key={customer.id} className="bg-neutral-50">
                <div className="flex flex-row gap-4 justify-between items-center">
                  <div className="flex flex-col p-2">
                    <div className="flex flex-col gap-4">
                      <div className="text-sm text-neutral-500">
                        担当: {customer?.agentUser?.name}
                      </div>
                      <div className="text-sm text-neutral-500">
                        住所: {customer?.address || "-"}
                      </div>
                      <div className="text-sm text-neutral-500">
                        年齢: {customer?.age ? `${customer.age}歳` : "-"}
                      </div>
                      <div className="text-sm text-neutral-500">
                        収入: {customer?.income || "-"}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="flex flex-col bg-neutral-50 rounded-md">
          <div className="flex flex-row gap-2 items-center justify-between border-b border-neutral-200 pb-2 px-4 pt-2">
            <div className="text-lg font-bold">顧客ニーズ</div>
            <Link
              href={`/ex/need/new?customerId=${id}`}
              className="flex flex-row gap-4"
            >
              <Button variant="outline">ニーズを追加</Button>
            </Link>
          </div>
          <div className="p-4">
            <DataTable
              data={customer?.needs || []}
              columns={needColumns}
              searchColumn="name"
              showFooter={false}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
