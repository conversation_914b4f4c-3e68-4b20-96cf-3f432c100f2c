"use client";

import { Separator } from "@/components/ui/separator";
import { useParams } from "next/navigation";
import {
  fetchCustomersAction,
  updateCustomerAction,
} from "@/actions/customers";
import { CustomerProps } from "@/lib/definitions";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { toast } from "@/hooks/use-toast";
import { useAuthStore } from "@/store/auth";
import CustomerForm from "../../CustomerForm";

export default function CustomerEditPage() {
  const { id } = useParams();
  const router = useRouter();
  const { currentUser } = useAuthStore();

  const [customer, setCustomer] = useState<CustomerProps | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);

  const fetchCustomer = async () => {
    const response = await fetchCustomersAction(id as string);
    if (response.success && response.data.length > 0) {
      const customerData = response.data[0] as CustomerProps;
      setCustomer(customerData);
    }
  };

  useEffect(() => {
    if (id) {
      fetchCustomer();
    }
  }, [id]);

  const handleUpdate = async (formData: any) => {
    setIsUpdating(true);

    try {
      console.log("🔥 Updating customer with data:", formData);

      const updateData = {
        id: customer?.id,
        name: formData.name,
        email: formData.email,
        phone: formData.phone,
        wechatAccount: formData.wechatAccount || undefined,
        address: formData.address,
        age: formData.age ? parseInt(formData.age) : undefined,
        income: formData.income ? parseInt(formData.income) : undefined,
        financialAsset: formData.financialAsset
          ? parseInt(formData.financialAsset)
          : undefined,
        propertyOwned: formData.propertyOwned || undefined,
        language: formData.language || undefined,
        mainNeedType: formData.mainNeedType || undefined,
        source: formData.source || undefined,
        // Only include agentUserId if user has permission to change it
        ...(currentUser?.accessLevel &&
          currentUser.accessLevel >= 90 && {
            agentUserId: formData.agentUserId || null,
          }),
      };

      const response = await updateCustomerAction(updateData);

      if (response.success) {
        toast({
          title: "更新完了",
          description: "顧客情報を更新しました",
          variant: "default",
        });

        router.push(`/pa/customer/`);
      } else {
        toast({
          title: "更新エラー",
          description: response.message || "更新に失敗しました",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "更新エラー",
        description: "更新中にエラーが発生しました",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  if (!customer) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-sm text-neutral-500">読み込み中...</div>
      </div>
    );
  }

  // Prepare initial data for the form
  const initialData = {
    name: customer.name || "",
    email: customer.email || "",
    phone: customer.phone || "",
    wechatAccount: customer.wechatAccount || "",
    address: customer.address || "",
    age: customer.age?.toString() || "",
    income: customer.income?.toString() || "",
    financialAsset: customer.financialAsset?.toString() || "",
    propertyOwned: customer.propertyOwned || "",
    agentUserId: customer.agentUserId || "",
    language: customer.language || "",
    mainNeedType: customer.mainNeedType || "",
    source: customer.source || "",
  };

  return (
    <div>
      <div className="flex justify-between items-center p-4">
        <h1 className="text-2xl font-bold" aria-label="顧客編集">
          顧客編集
        </h1>
      </div>

      <Separator />

      <CustomerForm
        initialData={initialData}
        onSubmit={handleUpdate}
        isLoading={isUpdating}
        submitButtonText="更新"
        onCancel={() => router.push(`/pa/customer/`)}
      />
    </div>
  );
}
