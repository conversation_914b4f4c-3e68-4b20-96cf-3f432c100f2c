"use client";

import { Separator } from "@/components/ui/separator";
import { useParams } from "next/navigation";
import {
  fetchCustomersAction,
  updateCustomerAction,
} from "@/actions/customers";

import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { CustomerProps } from "@/lib/definitions";
import { TllUserProps } from "@/lib/definitions/tllUser";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { getListOfAgentUserAction } from "@/actions/tllUserLambdaRecordFromSumifuAction";
import {
  Select,
  SelectValue,
  SelectContent,
  SelectTrigger,
  SelectItem,
} from "@/components/ui/select";
import { toast } from "@/hooks/use-toast";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { useAuthStore } from "@/store/auth";
export default function CustomerEditPage() {
  const { id } = useParams();
  const router = useRouter();
  const { currentUser } = useAuthStore();

  const [customer, setCustomer] = useState<CustomerProps | null>(null);
  const [agentUsers, setAgentUsers] = useState<TllUserProps[]>([]);

  const [isUpdating, setIsUpdating] = useState(false);

  // Form state for all customer fields
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    address: "",
    age: "",
    income: "",
    agentUserId: "",
  });

  // 🔥 Debug current form state
  console.log("🔥 Current formData.agentUserId:", formData.agentUserId);

  const fetchCustomer = async () => {
    const response = await fetchCustomersAction(id as string);
    if (response.success && response.data) {
      const customerData = response.data[0];
      setCustomer(customerData);

      // Populate form with customer data
      console.log(
        "🔥 Loading customer data, agentUserId:",
        customerData.agentUserId,
      );

      setFormData({
        name: customerData.name || "",
        email: customerData.email || "",
        phone: customerData.phone || "",
        address: customerData.address || "",
        age: customerData.age?.toString() || "",
        income: customerData.income?.toString() || "",
        agentUserId: customerData.agentUserId || "",
      });

      console.log(
        "🔥 Form data set with agentUserId:",
        customerData.agentUserId || "",
      );
    }
  };

  useEffect(() => {
    getListOfAgentUserAction().then((res) => {
      if (res.success && res.data) {
        setAgentUsers(res.data);
      }
    });

    if (id) {
      fetchCustomer();
    }
  }, [id]);

  const handleUpdate = async () => {
    if (!formData.name.trim()) {
      toast({
        title: "入力エラー",
        description: "顧客名は必須です",
        variant: "destructive",
      });
      return;
    }

    // 🔥 Agent can be null (unassigned), so no validation needed

    setIsUpdating(true);

    try {
      console.log("🔥 Saving customer with agentUserId:", formData.agentUserId);

      const updateData = {
        id: customer?.id,
        name: formData.name,
        email: formData.email,
        phone: formData.phone,
        address: formData.address,
        age: formData.age ? parseInt(formData.age) : undefined,
        income: formData.income ? parseInt(formData.income) : undefined,
        // Only include agentUserId if user has permission to change it
        ...(currentUser?.accessLevel &&
          currentUser.accessLevel >= 90 && {
            agentUserId: formData.agentUserId || null,
          }),
      };

      console.log("🔥 Full update data being sent:", updateData);

      const response = await updateCustomerAction(updateData);

      console.log("🔥 Update response:", response);

      if (response.success) {
        toast({
          title: "更新完了",
          description: "顧客情報を更新しました",
          variant: "default",
        });

        // Don't auto-navigate so we can see what happens to the form
        console.log(
          "🔥 Save successful, form should maintain 未割り当て selection",
        );

        router.push(`/pa/customer/`);
      } else {
        toast({
          title: "更新エラー",
          description: response.message || "顧客情報を更新できませんでした",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "更新エラー",
        description: "更新中にエラーが発生しました",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center p-4">
        <h1 className="text-2xl font-bold" aria-label="顧客情報修正">
          顧客情報修正
        </h1>
      </div>

      <Separator className="" />

      {/* {!customer ? (
        <div>
          <Loader2 className="w-4 h-4 animate-spin" />
        </div>
      ) : (
        <div key={customer.id} className="bg-neutral-50 p-4">
          <div className="flex flex-row gap-4 justify-between items-center">
            <div className="flex flex-col">
              <div className="text-lg font-bold">{customer.name}</div>
              <div className="flex flex-row gap-4">
                <div className="text-sm text-neutral-500">
                  担当: {customer?.agentUser?.name}
                </div>
                <div className="text-sm text-neutral-500">
                  住所: {customer?.address || "-"}
                </div>
                <div className="text-sm text-neutral-500">
                  年齢: {customer?.age ? `${customer.age}歳` : "-"}
                </div>
                <div className="text-sm text-neutral-500">
                  収入: {customer?.income || "-"}
                </div>
              </div>
            </div>
          </div>

          <div className="mt-2">
            <DataTable
              data={customer.needs || []}
              columns={needColumns}
              searchColumn="name"
              showFooter={false}
            />
          </div>
        </div>
      )} */}

      <div className="flex flex-col gap-4 p-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex flex-col gap-2">
            <Label htmlFor="name">顧客名 *</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) =>
                setFormData({ ...formData, name: e.target.value })
              }
              placeholder="顧客名を入力"
              required
            />
          </div>

          <div className="flex flex-col gap-2">
            <Label htmlFor="email">メールアドレス</Label>
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) =>
                setFormData({ ...formData, email: e.target.value })
              }
              placeholder="メールアドレスを入力"
            />
          </div>

          <div className="flex flex-col gap-2">
            <Label htmlFor="phone">電話番号</Label>
            <Input
              id="phone"
              value={formData.phone}
              onChange={(e) =>
                setFormData({ ...formData, phone: e.target.value })
              }
              placeholder="電話番号を入力"
            />
          </div>

          <div className="flex flex-col gap-2">
            <Label htmlFor="address">住所</Label>
            <Input
              id="address"
              value={formData.address}
              onChange={(e) =>
                setFormData({ ...formData, address: e.target.value })
              }
              placeholder="住所を入力"
            />
          </div>

          <div className="flex flex-col gap-2">
            <Label htmlFor="age">年齢</Label>
            <Input
              id="age"
              type="number"
              value={formData.age}
              onChange={(e) =>
                setFormData({ ...formData, age: e.target.value })
              }
              placeholder="年齢を入力"
            />
          </div>

          <div className="flex flex-col gap-2">
            <Label htmlFor="income">年収</Label>
            <Input
              id="income"
              type="number"
              value={formData.income}
              onChange={(e) =>
                setFormData({ ...formData, income: e.target.value })
              }
              placeholder="年収を入力"
            />
          </div>
        </div>

        {/* Agent Assignment - Only editable for users with accessLevel >= 90 */}
        <div className="flex flex-col gap-2">
          <Label>担当者</Label>
          {currentUser?.accessLevel && currentUser.accessLevel >= 90 ? (
            <Select
              value={formData.agentUserId ? formData.agentUserId : "unassigned"}
              onValueChange={(value) => {
                console.log("🔥 Select onValueChange:", value);
                setFormData({
                  ...formData,
                  agentUserId: value === "unassigned" ? "" : value,
                });
                console.log(
                  "🔥 Form data updated, agentUserId:",
                  value === "unassigned" ? "" : value,
                );
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="担当者を選択" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="unassigned">未割り当て</SelectItem>
                {agentUsers.map((user) => (
                  <SelectItem key={user.id} value={user.id || ""}>
                    {user.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          ) : (
            <Input
              value={customer?.agentUser?.name || "未割り当て"}
              disabled
              className="bg-neutral-100"
            />
          )}
        </div>

        <div className="flex justify-start gap-2">
          <Button
            variant="outline"
            onClick={handleUpdate}
            disabled={isUpdating}
          >
            {isUpdating && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
            更新
          </Button>
          <Button
            variant="ghost"
            onClick={() => router.push(`/pa/customer/${id}`)}
            disabled={isUpdating}
          >
            キャンセル
          </Button>
        </div>
      </div>
    </div>
  );
}
