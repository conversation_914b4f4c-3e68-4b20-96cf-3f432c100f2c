"use client";

import dayjs from "dayjs";
import {
  detectPriceIncreaseAfterOneMonth,
  getPriceChangeHistory,
} from "@/components/userLambdaRecord/priceChangeUtilities";
import { PriceChangeChartInTable } from "@/components/userLambdaRecord/PriceChangeChartInTable";
import Link from "next/link";
import { getStatus } from "@/lib/userLambdaRecord/getStatus";

export const getSnsTableColumn = (
  optimizeForPortrait: boolean,
  filterRecordType: string,
  hideAdminButton: boolean,
  isOnDashboard: boolean = false,
) => {
  let mapper = [
    {
      optimizeForPortrait: false,
      header: "#",
      cell: ({ row }: { row: any }) => row.index + 1,
    },
    {
      optimizeForPortrait: true,
      header: "所在地",
      accessorKey: "address",
      cell: ({ row }: { row: any }) => {
        return (
          <>
            {detectPriceIncreaseAfterOneMonth(row.original.priceChanges) !==
            null ? (
              <div
                style={{
                  fontWeight: "bold",
                }}
              >
                [再販]
              </div>
            ) : (
              ""
            )}
            <span
              style={{
                whiteSpace: "pre-line",
              }}
            >
              {row.original.address.replace(/(区|市)/g, "$1\n")}
            </span>
          </>
        );
      },
    },
    {
      optimizeForPortrait: false,
      header: "タイプ",
      accessorKey: "recordSubType",
      cell: ({ row }: { row: any }) => row.original.recordSubType?.slice(0, 5),
    },
    {
      optimizeForPortrait: false,
      header: "最寄駅",
      accessorKey: "nearestStation",
      cell: ({ row }: { row: any }) => {
        return (
          <>
            {row.original.nearestStation}
            {row.original.nearestStationWalkMinute > 0 &&
            row.original.nearestStationWalkMinute <= 30 ? (
              <>
                <br />
                徒歩{row.original.nearestStationWalkMinute}分
              </>
            ) : (
              <>
                <br />
                バス便
              </>
            )}
          </>
        );
      },
    },

    {
      optimizeForPortrait: true,
      header: "土地(㎡)",
      cell: ({ row }: { row: any }) => {
        return <>{parseInt(row.original.landSize)} </>;
      },
    },
    {
      optimizeForPortrait: true,
      header: "延床(㎡)",
      // (
      //   <>
      //     延床面積
      //     < div className="title-desc" >
      //       (m < sup > 2 </sup>)
      //     </div>
      //   </>
      // ),
      cell: ({ row }: { row: any }) => {
        return <>{parseInt(row.original.buildingSize)} </>;
      },
    },
    {
      optimizeForPortrait: true,
      // .replace('RC', 'RC造')
      header: "構造",
      accessorKey: "buildingMaterial",
      cell: ({ row }: { row: any }) => {
        return (
          <>
            {row.original.buildingMaterial
              .split("造")[0]
              .replace("ＲＣ", "RC")
              .replace("鉄筋コンクリート", "RC")}{" "}
          </>
        );
      },
    },
    {
      optimizeForPortrait: true,
      header: "築年",
      accessorKey: "buildingBuiltYear",
    },
    {
      dashboard: true,
      filterRecordType: "BUILDING",
      optimizeForPortrait: true,
      header: "年収入",
      // (
      //   <>
      //     年収入
      //     < div className="title-desc" > (万円) </div>
      //   </>
      // ),
      accessorKey: "yearlyIncome",
      cell: ({ row }: { row: any }) => {
        return (
          <>
            {row.original.yearlyIncome > 0
              ? parseInt(row.original.yearlyIncome)
              : "-"}{" "}
          </>
        );
      },
    },
    {
      optimizeForPortrait: true,
      header: "改定前",
      // (
      //   <>
      //     改定前
      //     {filterRecordType === 'BUILDING' && <div className="title-desc" > (利回) </div>}
      //   </>
      // ),
      accessorKey: "priceChangeHistory",
      className: "price-column",
      cell: ({ row }: { row: any }) => {
        let priceChangeHistory = getPriceChangeHistory(
          row.original.price,
          row.original.priceChanges,
        )?.rawData;

        let priceChangePrev = priceChangeHistory?.priceChangePrev;

        return priceChangePrev ? (
          <>
            {priceChangePrev}万
            {filterRecordType === "BUILDING" && (
              <>
                <br />(
                {row.original.yearlyIncome > 0
                  ? (
                      (row.original.yearlyIncome / (priceChangePrev || 0)) *
                      100
                    ).toFixed(1) + "%"
                  : "-"}
                )
              </>
            )}
          </>
        ) : (
          "-"
        );
      },
    },
    {
      optimizeForPortrait: true,
      header: "=>",
      accessorKey: "sign",
      className: "price-column",
      cell: ({ row }: { row: any }) => {
        return `=>`;
      },
    },
    {
      optimizeForPortrait: true,
      header: "現在",
      // (
      //   <div>
      //     現在
      //     {filterRecordType === 'BUILDING' && <div className="title-desc" > (利回) </div>}
      //   </div>
      // ),
      accessorKey: "currentPrice",
      className: "price-column",
      cell: ({ row }: { row: any }) => {
        return (
          <>
            {row.original.price}万
            {filterRecordType === "BUILDING" && (
              <>
                <br />(
                {row.original.yearlyIncome > 0
                  ? (
                      (row.original.yearlyIncome / row.original.price) *
                      100
                    ).toFixed(1) + "%"
                  : "-"}
                )
              </>
            )}
          </>
        );
      },
    },
    {
      header: "差額",
      // (
      //   <>
      //     差额
      //     < div className="title-desc" > (▼割合)</div>
      //   </>
      // ),
      accessorKey: "diffPrice",
      className: "price-column",
      cell: ({ row }: { row: any }) => {
        const priceChangeHistory = getPriceChangeHistory(
          row.original.price,
          row.original.priceChanges,
        )?.rawData;
        const priceChangeRecent = priceChangeHistory?.priceChangeRecent;
        const priceChangePerc =
          priceChangeHistory?.priceChangePerc &&
          priceChangeHistory?.priceChangePerc * 100;
        const isPriceIncrease = priceChangeRecent && priceChangeRecent > 0;

        return priceChangeRecent ? (
          <div style={{ fontWeight: "bold", textAlign: "center" }}>
            <div
              style={{
                color: isPriceIncrease ? "red" : "green",
              }}
            >
              {isPriceIncrease ? "▲" : "▼"}
              {priceChangeRecent && Math.abs(priceChangeRecent)} 万
            </div>
            <div
              style={{
                color: isPriceIncrease ? "red" : "green",
              }}
            >
              ({isPriceIncrease ? "▲" : "▼"}
              {priceChangePerc && Math.abs(priceChangePerc).toFixed(1)}%)
            </div>
          </div>
        ) : (
          "-"
        );
      },
    },
    {
      optimizeForPortrait: false,
      header: "経過日数",
      accessorKey: "elapsedDays",
      cell: ({ row }: { row: any }) => {
        const priceChangePrevDate = getPriceChangeHistory(
          row.original.price,
          row.original.priceChanges,
        )?.rawData?.priceChangePrevDate;

        return <>{dayjs().diff(dayjs(priceChangePrevDate), "days")} </>;
      },
    },

    {
      dashboard: true,
      header: "改正推移",
      // width: optimizeForPortrait ? 80 : 240,
      cell: ({ row }: { row: any }) => {
        return <PriceChangeChartInTable record={row.original} />;
      },
    },
    {
      adminButton: true,
      header: "リンク",
      cell: ({ row }: { row: any }) => (
        <Link
          href={`/ex/search/${row.original.id}`}
          style={{
            color: "black",
            textDecoration: "underline",
          }}
        >
          {row.original.id.slice(0, 5)}
        </Link>
      ),
    },
    {
      dashboard: true,
      adminButton: true,
      header: "状态",
      accessorKey: "status",
      cell: ({ row }: { row: any }) => <span>{getStatus(row.original)} </span>,
    },
  ];

  let flteredMapper = mapper.filter((v: any) => {
    if (hideAdminButton && v.adminButton) {
      return false;
    }

    if (isOnDashboard && v.dashboard) {
      return false;
    }

    if (
      v.filterRecordType !== undefined &&
      v.filterRecordType !== filterRecordType
    ) {
      return false;
    }

    if (optimizeForPortrait && v.optimizeForPortrait === false) {
      return false;
    }

    return true;
  });

  return flteredMapper;
};
