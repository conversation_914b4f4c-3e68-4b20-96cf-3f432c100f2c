"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Suspense, useEffect, useRef, useState } from "react";
import dayjs from "dayjs";
import "dayjs/locale/ja"; // Import Japanese locale
dayjs.locale("ja"); // Set locale to Japanese

import html2canvas from "html2canvas";
import { Separator } from "@/components/ui/separator";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import MultipleSelect from "react-select";
import { Loader } from "lucide-react";
import { getSnsTableColumn } from "./getSnsTableColumn";
import { getUserLambdaRecordForSNS } from "@/actions/tllUserLambdaRecords";
import { toast } from "@/hooks/use-toast";
import { UserLambdaRecordType } from "@/lib/definitions/userLambdaRecord";
import SNSPostBox from "./SNSPostBox";

export default function SNSBukkenImage() {
  const cardRef = useRef<HTMLDivElement>(null);

  const [dataDate, setDataDate] = useState<string>("yesterday");
  const [dataPrefecture, setDataPrefecture] = useState<any[]>([]);
  const [dataRecordType, setDataRecordType] = useState<UserLambdaRecordType>(
    UserLambdaRecordType.BUILDING,
  );

  const [lan, setLan] = useState("ja");
  const [columns, setColumns] = useState<any[]>([]);
  const [filterDataCount, setFilterDataCount] = useState<string>("999");
  const [filterRoi, setFilterRoi] = useState<any>();
  const [showPriceType, setShowPriceType] = useState<string>("");
  const [sortBy, setSortBy] = useState<string>("priceChangePerc");
  const [optimizeForPortrait, setOptimizeForPortrait] = useState<boolean>(true);
  const [hideAdminButton, setHideAdminButton] = useState<boolean>(true);
  const [hideUrba, setHideUrba] = useState<boolean>(false);
  const [reinsdata, setReinsdata] = useState<any[]>([]);
  const [loadingReins, setLoadingReins] = useState<boolean>(false);
  const [loadingExport, setLoadingExport] = useState<boolean>(false);

  useEffect(() => {
    setColumns(
      getSnsTableColumn(optimizeForPortrait, dataRecordType, hideAdminButton),
    );
  }, [optimizeForPortrait, dataRecordType, hideAdminButton]);

  const getDataForReinsSnS = async () => {
    setLoadingReins(true);
    setReinsdata([]);

    let data = await getUserLambdaRecordForSNS({
      dataDate,
      dataPrefecture,
      dataRecordType,
    });

    if (!data.success) {
      toast({
        title: "データを取得できませんでした",
        description: data.message,
      });
    } else {
      setReinsdata(data.data);
    }

    setLoadingReins(false);
  };

  const prefectureOptions = [
    { label: "埼玉県", value: 11 },
    { label: "千葉県", value: 12 },
    { label: "東京都", value: 13 },
    { label: "神奈川県", value: 14 },
  ];

  return (
    <div>
      <div className="flex justify-between items-center p-4 -mb-2">
        <h1 className="text-2xl font-bold">SNS掲載</h1>

        <div className="flex items-center">
          <Button
            onClick={async () => {
              if (!cardRef.current) {
                return;
              }
              setLoadingExport(true);

              const canvas = await html2canvas(cardRef.current);

              const imgData = canvas.toDataURL("image/png");
              const link = document.createElement("a");
              link.href = imgData;
              link.download = "download.png";
              // link.style.display = 'none';
              // document.body.appendChild(link);
              link.click();
              // document.body.removeChild(link);

              setLoadingExport(false);
            }}
          >
            {loadingExport ? (
              <Loader className="w-4 h-4 animate-spin" />
            ) : (
              "画像出力"
            )}
          </Button>
        </div>
      </div>

      <Separator className="my-2" />

      <div className="p-2 sm:p-4 flex">
        <div className="p-2 flex flex-col gap-2">
          <span className="text-sm font-bold">データ取得</span>
          <Select
            value={dataDate}
            onValueChange={(value: any) => {
              setDataDate(value);
            }}
          >
            <SelectTrigger>
              <SelectValue placeholder="日付を選択" />
            </SelectTrigger>

            <SelectContent>
              {[
                { name: "今日", code: "today" },
                { name: "昨日", code: "yesterday" },
                { name: "三日間", code: "threeDays" },
                { name: "一週間", code: "oneWeek" },
              ].map((recordType) => (
                <SelectItem key={recordType.code} value={recordType.code}>
                  {recordType.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <MultipleSelect
            instanceId="post-multiselect"
            aria-activedescendant="post-multiselect"
            placeholder="都道府県を選択"
            className="w-[200px]"
            styles={{
              control: (base, state) => ({
                ...base,
                backgroundColor: "white",
                borderColor: state.isFocused ? "#3b82f6" : "#e5e7eb",
                boxShadow: state.isFocused ? "0 0 0 2px #3b82f6" : "none",
                "&:hover": { borderColor: "#3b82f6" },
              }),
              multiValue: (base) => ({
                ...base,
                backgroundColor: "#eef2ff",
                color: "#3730a3",
                borderRadius: "6px",
                padding: "2px 6px",
                whiteSpace: "nowrap", // ❌ Prevents text from breaking
              }),
              multiValueLabel: (base) => ({
                ...base,
                color: "#3730a3",
                fontWeight: "500",
              }),
              multiValueRemove: (base) => ({
                ...base,
                color: "#6366f1",
                "&:hover": { backgroundColor: "#c7d2fe", color: "#1e3a8a" },
              }),
            }}
            isMulti
            options={prefectureOptions}
            value={dataPrefecture}
            onChange={setDataPrefecture as any}
          />

          <Select
            value={dataRecordType}
            onValueChange={(value: any) => {
              setDataRecordType(value);
            }}
          >
            <SelectTrigger>
              <SelectValue placeholder="レコードタイプを選択" />
            </SelectTrigger>
            <SelectContent>
              {[
                { name: "一棟収益", code: UserLambdaRecordType.BUILDING },
                { name: "一戸建て", code: UserLambdaRecordType.HOUSE },
                { name: "土地", code: UserLambdaRecordType.LAND },
              ].map((recordType) => (
                <SelectItem key={recordType.code} value={recordType.code}>
                  {recordType.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Button
            variant="default"
            disabled={
              loadingReins ||
              !dataDate ||
              !dataRecordType ||
              !dataPrefecture.length
            }
            onClick={() => {
              getDataForReinsSnS();
            }}
          >
            {loadingReins ? (
              <Loader className="w-4 h-4 animate-spin" />
            ) : (
              "データ取得"
            )}
          </Button>

          <Separator className="my-2" />

          <span className="text-sm font-bold">データ出力調整</span>

          <Select
            value={filterDataCount.toString()}
            onValueChange={(value: any) => {
              setFilterDataCount(value);
            }}
          >
            <SelectTrigger>
              <SelectValue placeholder="データ数を選択" />
            </SelectTrigger>

            <SelectContent>
              {[
                { name: "5件", code: 5 },
                { name: "10件", code: 10 },
                { name: "20件", code: 20 },
                { name: "全て", code: 999 },
              ].map((dataCount) => (
                <SelectItem
                  key={dataCount.code}
                  value={dataCount.code.toString()}
                >
                  {dataCount.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select
            value={filterRoi}
            onValueChange={(value: any) => {
              setFilterRoi(value);
            }}
          >
            <SelectTrigger>
              <SelectValue placeholder="利回りを選択" />
            </SelectTrigger>
            <SelectContent>
              {[
                { name: "利回り > 5%", code: 5 },
                { name: "利回り > 8%", code: 8 },
                { name: "利回り > 10%", code: 10 },
                { name: "全て", code: 0 },
              ].map((roi) => (
                <SelectItem key={roi.code} value={roi.code?.toString()}>
                  {roi.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select
            value={sortBy || ""}
            onValueChange={(value: any) => {
              setSortBy(value);
            }}
          >
            <SelectTrigger>
              <SelectValue placeholder="並び替えを選択" />
            </SelectTrigger>
            <SelectContent>
              {[
                { name: "並び替えなし", code: "all" },
                { name: "改正価格⬇", code: "priceChange" },
                { name: "改正割合⬇", code: "priceChangePerc" },
              ].map((sortBy) => (
                <SelectItem key={sortBy.code} value={sortBy.code?.toString()}>
                  {sortBy.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select
            value={showPriceType}
            onValueChange={(value: any) => {
              setShowPriceType(value);
            }}
          >
            <SelectTrigger>
              <SelectValue placeholder="価格を選択" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="すべて">すべて</SelectItem>
              <SelectItem value="価格上昇のみ">価格上昇のみ</SelectItem>
              <SelectItem value="価格減少のみ">価格減少のみ</SelectItem>
            </SelectContent>
          </Select>

          <Select
            value={lan}
            onValueChange={(value) => {
              setLan(value);
            }}
          >
            <SelectTrigger>
              <SelectValue placeholder="言語を選択" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="ja">日本語</SelectItem>
              <SelectItem value="zh">中国語</SelectItem>
            </SelectContent>
          </Select>

          <Button
            variant={hideUrba ? "default" : "secondary"}
            onClick={() => {
              setHideUrba(!hideUrba);
            }}
          >
            Urbaを隠す
          </Button>

          <Button
            variant={hideAdminButton ? "default" : "secondary"}
            onClick={() => {
              setHideAdminButton(!hideAdminButton);
            }}
          >
            管理ボタン隠す
          </Button>

          <Button
            variant={optimizeForPortrait ? "default" : "secondary"}
            onClick={() => {
              setColumns(
                getSnsTableColumn(
                  !optimizeForPortrait,
                  dataRecordType,
                  hideAdminButton,
                ),
              );
              setOptimizeForPortrait(!optimizeForPortrait);
            }}
          >
            ポートレイト
          </Button>
        </div>

        <div className="h-full w-full p-4" ref={cardRef}>
          <SNSPostBox
            dataDate={dataDate}
            dataPrefecture={dataPrefecture}
            showPriceType={showPriceType}
            dataRecordType={dataRecordType}
            columns={columns}
            tableData={reinsdata}
            loadingReins={loadingReins}
            lan={lan}
            hideUrba={hideUrba}
            sortBy={sortBy}
            filterDataCount={filterDataCount}
            showFooter={true}
            filterRoi={filterRoi}
          />
        </div>
      </div>
    </div>
  );
}
