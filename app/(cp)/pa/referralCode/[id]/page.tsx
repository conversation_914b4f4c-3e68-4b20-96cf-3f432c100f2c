"use client";

import { getReferralCode } from "@/actions/referralCode";
import { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import { ReferralCodeProps } from "@/lib/definitions/referralCode";
import { Users } from "lucide-react";
import { Button } from "@/components/ui/button";
import { DataTable } from "@/components/ui/data-table";
import dayjs from "@/lib/thirdParty/dayjsWithTz";
import { SystemUserActivityProps } from "@/lib/definitions/system";
import { mapper } from "@/app/(cp)/an/(common)/recordTypeMapper";

export default function ReferralCodePage() {
  const { id } = useParams();
  const [referralCode, setReferralCode] = useState<ReferralCodeProps | null>(
    null,
  );
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const fetchReferralCode = async () => {
      const res = await getReferralCode(id as string);
      if (res.success) {
        setReferralCode(res.data);
      }
    };

    fetchReferralCode();
  }, [id]);

  const getTopSearchTypes = (searchHistory: SystemUserActivityProps[]) => {
    // 1. 先提取有效的搜索标签（格式: 駅名（タイプ））
    const searchLabels = searchHistory
      .filter(
        (item) =>
          item.eventMetadata?.selectRecordType &&
          item.eventMetadata?.selectedOption?.name,
      )
      .map((item) => {
        const type =
          mapper[item.eventMetadata?.selectRecordType as keyof typeof mapper]
            ?.nameFull || "";
        return `${item.eventMetadata?.selectedOption?.name}（${type}）`;
      });

    // 2. 统计频次
    const countMap: Record<string, number> = {};
    searchLabels.forEach((label) => {
      countMap[label] = (countMap[label] || 0) + 1;
    });

    // 3. 排序并截取前 topX 个
    const topX = 5;
    const sorted = Object.entries(countMap)
      .sort((a, b) => b[1] - a[1]) // 按照次数降序排列
      .slice(0, topX)
      .map(([label, count]) => `${label} × ${count}`);

    return sorted;
  };

  let columns = [
    {
      header: "名前",
      accessorKey: "name",
    },
    {
      header: "メール",
      accessorKey: "email",
    },
    {
      header: "最終ログイン日時",
      accessorKey: "lastLoginAt",
      cell: ({ row }: { row: any }) => {
        return (
          <div>
            {dayjs(row.original.lastLoginAt).format("YYYY-MM-DD HH:mm:ss")} (
            {dayjs().diff(dayjs(row.original.lastLoginAt), "days")}日前)
          </div>
        );
      },
    },
    {
      header: "サブ履歴",
      accessorKey: "subscriptionStatus",
    },
    {
      header: "検索履歴",
      accessorKey: "searchHistory",
      cell: ({ row }: { row: any }) => {
        const topSearches = getTopSearchTypes(row.original.systemUserActivity);

        return (
          <div className="flex flex-col text-xs text-gray-700">
            {topSearches.map((item, idx) => (
              <div key={idx}>{item}</div>
            ))}
          </div>
        );
      },
    },
  ];

  console.log("🔥 [ReferralCodePage] referralCode:", referralCode);

  return (
    <div className="flex flex-col gap-4">
      <div className="p-4 flex flex-row gap-4 justify-between items-center border-b border-neutral-200">
        <h1 className="text-2xl font-bold">紹介コード: {referralCode?.code}</h1>
      </div>

      <div className="p-4 flex flex-col gap-4">
        <div className="flex flex-row gap-2 items-center">
          <Users className="w-4 h-4" />
          <p>利用者数: {referralCode?.referredUsers?.length}</p>
        </div>

        <DataTable
          columns={columns}
          data={referralCode?.referredUsers || []}
          isLoading={isLoading}
        />
      </div>
    </div>
  );
}
