"use client";

import { toast } from "@/hooks/use-toast";
import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import {
  getMyReferralCode,
  createReferralCode,
  updateReferralCode,
} from "@/actions/referralCode";
import { DataTable } from "@/components/ui/data-table";
import { ReferralCodeProps } from "@/lib/definitions/referralCode";
import { ColumnDef } from "@tanstack/react-table";
import { Separator } from "@/components/ui/separator";
import { Loader2, Trash } from "lucide-react";
import Link from "next/link";
import dayjs from "@/lib/thirdParty/dayjsWithTz";
import { Badge } from "@/components/ui/badge";

export default function ReferralCodePage() {
  const [loading, setLoading] = useState(false);
  const [codes, setCodes] = useState<ReferralCodeProps[]>([]);
  const [newCode, setNewCode] = useState<ReferralCodeProps | null>(null);

  const fetchReferralCode = async () => {
    setLoading(true);
    const res = await getMyReferralCode();

    if (res.success) {
      setCodes(res.data);
    }

    setLoading(false);
  };

  const handleCreate = async () => {
    let hasActiveCode = codes.some((code) => code.isActive);
    if (hasActiveCode) {
      const confirmed = window.confirm(
        "この操作は他の有効な紹介コードをすべて無効化します。続けますか？",
      );
      if (!confirmed) return;
    }

    setLoading(true);
    try {
      const res = await createReferralCode();
      if (!res.success) throw new Error(res.message);
      setNewCode(res.data);
      fetchReferralCode();
      toast({
        title: "紹介コードが作成されました",
        description: res.data.code,
      });
    } catch (err: any) {
      toast({
        title: "エラー",
        description: err.message,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const columns = [
    {
      header: "有効/無効",
      accessorKey: "isActive",
      cell: ({ row }) => {
        return (
          <div>
            {row.original.isActive ? (
              <Badge>有効</Badge>
            ) : (
              <Badge variant="destructive">無効</Badge>
            )}
          </div>
        );
      },
    },
    {
      header: "紹介コード",
      accessorKey: "code",
    },

    {
      header: "作成日時",
      accessorKey: "createdAt",
      cell: ({ row }) => {
        return (
          <div>
            {dayjs(row.original.createdAt).format("YYYY-MM-DD HH:mm:ss")}
          </div>
        );
      },
    },
    {
      header: "有効期限",
      accessorKey: "expiresAt",
      cell: ({ row }) => {
        return (
          <div className="flex flex-row gap-1 w-full justify-center items-center">
            {dayjs(row.original.expiresAt).format("YYYY-MM-DD HH:mm:ss")}

            <div className="text-xs text-neutral-600">
              ({dayjs(row.original.expiresAt).diff(dayjs(), "day")}日後)
            </div>
          </div>
        );
      },
    },
    {
      header: "利用者数",
      accessorKey: "referredUsers",
      cell: ({ row }) => {
        return row.original.referredUsers?.length ? (
          <Link
            href={`/pa/referralCode/${row.original.id}`}
            className="underline"
          >
            {row.original.referredUsers.length}
          </Link>
        ) : (
          <div></div>
        );
      },
    },
    {
      header: "操作",
      accessorKey: "actions",
      cell: ({ row }) => {
        return (
          <div className="flex flex-row gap-1 w-full justify-center items-center">
            {row.original.isActive && (
              <Button
                variant="outline"
                size="sm"
                onClick={async () => {
                  navigator.clipboard
                    .writeText(
                      `${window.location.origin}/my/account?tab=signUp&code=${row.original.code}`,
                    )
                    .then(() => {
                      toast({ title: "招待リンクをコピーしました" });
                    })
                    .catch((err) => {
                      toast({
                        title: "招待リンクをコピーに失敗しました",
                        description: err.message,
                        variant: "destructive",
                      });
                    });
                }}
              >
                招待リンクをコピー
              </Button>
            )}

            {row.original.isActive && (
              <Button
                variant="outline"
                size="sm"
                disabled={!row.original.isActive}
                onClick={async () => {
                  const res = await updateReferralCode(
                    row.original.id as string,
                    { isActive: !row.original.isActive },
                  );
                  if (!res.success) throw new Error(res.message);
                  toast({
                    title: "紹介コードが更新されました",
                    description: row.original.isActive ? "無効化" : "",
                  });
                  fetchReferralCode();
                }}
              >
                {row.original.isActive ? "無効化" : ""}
              </Button>
            )}
          </div>
        );
      },
    },
  ] as ColumnDef<ReferralCodeProps>[];

  useEffect(() => {
    fetchReferralCode();
  }, []);

  return (
    <div className="w-full h-full overflow-y-auto">
      <div className="flex justify-between items-center p-4 border-b border-neutral-200">
        <h1 className="text-2xl font-bold" aria-label="紹介コード">
          紹介コード
        </h1>
      </div>

      <div className="flex justify-between items-center p-4">
        <Button onClick={handleCreate} disabled={loading}>
          {loading ? (
            <Loader2 className="w-4 h-4 animate-spin" />
          ) : (
            "QRードを作成"
          )}
        </Button>

        {newCode && (
          <div className="p-4">
            <p className="text-sm text-neutral-600">新しい紹介コード</p>
            <p
              className="text-xl font-bold cursor-pointer hover:underline"
              onClick={() => {
                navigator.clipboard
                  .writeText(newCode.code || "")
                  .then(() => {
                    toast({
                      title: "コピーしました",
                      description: newCode.code,
                    });
                  })
                  .catch((err) => {
                    toast({
                      title: "コピーに失敗しました",
                      description: err.message,
                      variant: "destructive",
                    });
                  });
              }}
            >
              {newCode.code}
            </p>
          </div>
        )}
      </div>

      <Separator />

      <div className="p-4">
        <DataTable
          columns={columns}
          data={codes.sort((a, b) =>
            dayjs(b.isActive ? b.expiresAt : b.createdAt).diff(
              dayjs(a.isActive ? a.expiresAt : a.createdAt),
            ),
          )}
          isLoading={loading}
        />
      </div>
    </div>
  );
}
