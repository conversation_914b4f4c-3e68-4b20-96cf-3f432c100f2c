"use client";

import { useState } from "react";
import NeedForm from "../../../ex/need/[needId]/NeedForm";
import { CustomerNeedProps } from "@/lib/definitions";
import { toast } from "@/hooks/use-toast";
import { createCustomerNeedAction } from "@/actions/customerNeeds";
import { useSearchParams, useRouter, useParams } from "next/navigation";
import { LARK_URLS } from "@/lib/thirdParty/lark";
import { sendLark } from "@/lib/thirdParty/lark";
import { useAuthStore } from "@/store/auth";

export default function NewNeedPage() {
  const { currentUser } = useAuthStore();
  const [need, setNeed] = useState<any>({});
  const { customerId } = useParams<{ customerId: string }>();
  const router = useRouter();

  const createNeed = async () => {
    console.log("🔥 need", need);

    try {
      let dataForSaving = {
        type: need.type,
        priority: need.priority,
        priceFrom: need.priceFrom,
        priceTo: need.priceTo,
        roiFrom: need.roiFrom,
        landAreaFrom: need.landAreaFrom,
        landAreaTo: need.landAreaTo,
        buildingAreaFrom: need.buildingAreaFrom,
        buildingAreaTo: need.buildingAreaTo,
        yearFrom: need.yearFrom,
        nearestStationGroupIds: need.nearestStationGroups
          ?.map((station: any) => station.geoRailwayStationGroup.id)
          .join(","),
        nearestStationWalkMinuteTo: need.nearestStationWalkMinuteTo,
        postalCodes: need.postalCodes
          ?.map((postalCode: any) => postalCode.value)
          .join(","),
        landCanHotel: need.landCanHotel,
        comment: need.comment,
        title: need.title,
        description: need.description,
      } as CustomerNeedProps;

      const response = await createCustomerNeedAction({
        customerId: customerId as string,
        customerNeedData: dataForSaving as CustomerNeedProps,
      });

      if (response.success) {
        setNeed(response.data);
        toast({
          title: "更新成功",
          description: "ニーズが更新されました",
        });
        await sendLark({
          message: `[⚙️][顧客ニーズ作成][${currentUser?.name}は、customer: ${customerId} さんのニーズを作成しました]`,
          url: LARK_URLS.USER_ACTIVITY_CHANNEL,
        });
        router.push(`/ex/need/${response.data.id}`);
      } else {
        console.error("🚨 更新需求时出错:", response.message);
      }
    } catch (error) {
      console.error("🚨 更新需求时出错:", error);
      toast({
        title: "更新失败",
        description: "ニーズが更新されませんでした",
      });
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center p-4 border-b border-neutral-200">
        <h1 className="text-2xl font-bold" aria-label="ニーズ追加">
          ニーズ追加 For Customer: {customerId}
        </h1>
      </div>

      <NeedForm need={need} setNeed={setNeed} submit={createNeed} />
    </div>
  );
}
