"use client";

import { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import CFSection from "@/app/(cp)/it/cf/CFSection";
import MinpakuSimulator from "@/app/(cp)/it/minpaku/MinpakuSimulator";
import PriceCfCalculation from "./PriceCfCalculation";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { useAuthStore } from "@/store/auth";

interface ProfitabilityAnalysisProps {
  currentUserLambdaRecord?: UserLambdaRecordProps | null | undefined;
}

export default function ProfitabilityAnalysis({
  currentUserLambdaRecord,
}: ProfitabilityAnalysisProps) {
  const { currentUser } = useAuthStore();
  // Check if user has access to CF試算 (accessLevel > 30)
  const hasCfAnalysisAccess =
    currentUser?.accessLevel && currentUser.accessLevel > 30;

  const [activeTab, setActiveTab] = useState("cf");

  useEffect(() => {
    if (hasCfAnalysisAccess) {
      setActiveTab("cf-analysis");
    }
  }, [hasCfAnalysisAccess]);

  return (
    <section id="profitabilityAnalysis" className="bg-white">
      <div className="border-b border-neutral-200 p-2">
        <h2 className="text-xl font-semibold text-neutral-900">収益性試算</h2>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList
          className={`grid w-full m-2 ${hasCfAnalysisAccess ? "grid-cols-3" : "grid-cols-2"}`}
        >
          {hasCfAnalysisAccess && (
            <TabsTrigger value="cf-analysis">CF試算(TLL自社用)</TabsTrigger>
          )}
          <TabsTrigger value="cf">CFシミュレーション</TabsTrigger>
          <TabsTrigger value="minpaku">民泊収益試算</TabsTrigger>
        </TabsList>

        <TabsContent value="cf" className="mt-0">
          <div className="p-2">
            <CFSection
              currentUserLambdaRecord={currentUserLambdaRecord}
              showTitle={false}
            />
          </div>
        </TabsContent>

        <TabsContent value="minpaku" className="mt-0">
          <div className="p-2">
            <MinpakuSimulator
              initialParams={{
                basePricePerNight:
                  currentUserLambdaRecord?.recordType === "HOUSE"
                    ? 20000
                    : 30000,
                propertyCost: currentUserLambdaRecord?.price
                  ? Math.round(currentUserLambdaRecord.price)
                  : 10000,
              }}
              showTitle={false}
            />
          </div>
        </TabsContent>

        {hasCfAnalysisAccess && (
          <TabsContent value="cf-analysis" className="mt-0">
            <div className="p-2">
              <PriceCfCalculation
                currentUserLambdaRecord={currentUserLambdaRecord || null}
                type="search"
              />
            </div>
          </TabsContent>
        )}
      </Tabs>
    </section>
  );
}
