"use client";

import FooterForNonLogin from "@/components/footerForNonLogin";
import { Button } from "@/components/ui/button";
import { CheckCircle2 } from "lucide-react";
import { useTranslations } from "next-intl";
import Image from "next/image";
import TrustBy from "../TrustBy";
import Link from "next/link";
import { useState } from "react";

export default function FeatureTemplatePage({
  title,
  description,
  heroSectionTitle,
  heroSectionDescriptions,
  heroSectionImage,
  sections,
}: {
  title: string;
  description: string;
  heroSectionTitle: string;
  heroSectionDescriptions: string[];
  heroSectionImage: string;
  sections: {
    label: string;
    anchor: string;
    photo: string;
    title: string;
    salesPoint: string[];
  }[];
}) {
  const [openImage, setOpenImage] = useState<string | null>(null);
  const [selectedSection, setSelectedSection] = useState<string | null>(null);
  const t = useTranslations("Header");

  return (
    <div className="pt-(--header-height) pb-(--footer-height) h-screen overflow-y-auto grow overflow-x-hidden">
      <div className="flex flex-col items-center justify-center grow mb-[36px]">
        <div className="flex flex-col items-center justify-center p-4 mt-10 gap-4">
          <div className="text-4xl font-bold">{title}</div>

          <div className="text-base text-gray-500">{description}</div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 my-[40px] sm:my-[100px] max-w-[1200px] mx-auto p-4">
          <div className="flex flex-col gap-8">
            <div className="text-3xl font-bold leading-relaxed">
              {heroSectionTitle}
            </div>

            <ul className="space-y-2 text-muted-foreground">
              {heroSectionDescriptions.map((description, index) => (
                <li key={index} className="flex items-start gap-2">
                  <CheckCircle2 className="w-5 h-5 text-green-600 mt-1" />
                  <span dangerouslySetInnerHTML={{ __html: description }} />
                </li>
              ))}
            </ul>

            <Link href="/login?tab=signUp">
              <Button
                className="px-24 py-8 text-lg"
                size="lg"
                variant="default"
              >
                {t("signUpForFree")}
              </Button>
            </Link>
          </div>

          {/* Right: Illustration */}
          <div className="relative w-full flex flex-col justify-center items-center">
            <Image
              src={heroSectionImage}
              alt="Illustration"
              fill
              className="object-contain"
              priority
            />
          </div>
        </div>

        <div className="sticky top-0 w-full bg-neutral-700 text-white border-t border-b z-10 py-4 sm:py-0">
          <div className="flex justify-center items-center h-16 max-w-[1200px] mx-auto">
            <div className="flex gap-1 mx-auto overflow-x-auto scrollbar-hide px-4">
              <div className="flex gap-1 min-w-max">
                {sections.map((section, i) => (
                  <Button
                    key={i}
                    variant="ghost"
                    className={`flex items-center justify-center text-white hover:text-primary whitespace-nowrap flex-shrink-0 ${selectedSection === section.anchor ? "bg-primary" : ""}`}
                    onClick={() => setSelectedSection(section.anchor)}
                  >
                    <Link href={`#${section.anchor}`}>
                      <span>{section.label}</span>
                    </Link>
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </div>

        <div className="flex flex-col items-center justify-center gap-4 md:gap-8 mt-8 md:mt-16 w-full px-2">
          {sections.map((section, i) => (
            <div
              key={i}
              id={section.anchor}
              className={`min-h-[200px] w-full ${i % 2 === 1 ? "bg-neutral-100" : ""} py-8 md:py-[48px] scroll-mt-20 md:scroll-mt-36`}
            >
              <div className="w-full flex flex-col items-center justify-center">
                <div className="max-w-[1200px] w-full mx-auto px-2">
                  <div
                    className={`grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-8 items-center ${i % 2 === 1 ? "md:flex-row-reverse" : ""}`}
                  >
                    {i % 2 === 0 ? (
                      <>
                        <div className="flex justify-center items-center w-full h-full">
                          <button
                            type="button"
                            onClick={() => setOpenImage(section.photo)}
                            className="focus:outline-hidden"
                          >
                            <Image
                              src={section.photo}
                              alt={section.label}
                              width={600}
                              height={400}
                              className="rounded-xl border border-gray-200 shadow-lg max-w-full sm:max-w-[80%] h-auto object-contain transition-transform hover:scale-105 cursor-zoom-in"
                            />
                          </button>
                        </div>
                        <div className="flex flex-col justify-center">
                          <div className="flex flex-col gap-2 text-neutral-400 text-xs md:text-sm mb-2">
                            {section.label}
                          </div>
                          <div className="text-xl md:text-2xl font-bold text-left mb-4">
                            {section.title}
                          </div>
                          <ul className="space-y-2 text-muted-foreground">
                            {section.salesPoint.map((item, j) => (
                              <li key={j} className="flex items-start gap-2">
                                <CheckCircle2 className="w-5 h-5 text-green-600 mt-1" />
                                <span
                                  dangerouslySetInnerHTML={{ __html: item }}
                                />
                              </li>
                            ))}
                          </ul>
                        </div>
                      </>
                    ) : (
                      <>
                        <div className="flex flex-col justify-center">
                          <div className="flex flex-col gap-2 text-neutral-400 text-xs md:text-sm mb-2">
                            {section.label}
                          </div>
                          <div className="text-xl md:text-2xl font-bold text-left mb-4">
                            {section.title}
                          </div>
                          <ul className="space-y-2 text-muted-foreground">
                            {section.salesPoint.map((item, j) => (
                              <li key={j} className="flex items-start gap-2">
                                <CheckCircle2 className="w-5 h-5 text-green-600 mt-1" />
                                <span
                                  dangerouslySetInnerHTML={{ __html: item }}
                                />
                              </li>
                            ))}
                          </ul>
                        </div>
                        <div className="flex justify-center items-center w-full">
                          <button
                            type="button"
                            onClick={() => setOpenImage(section.photo)}
                            className="focus:outline-hidden"
                          >
                            <Image
                              src={section.photo}
                              alt={section.label}
                              width={600}
                              height={400}
                              className="rounded-xl border border-gray-200 shadow-lg max-w-full sm:max-w-[80%] h-auto object-contain transition-transform hover:scale-105 cursor-zoom-in"
                            />
                          </button>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {openImage && (
        <div
          className="fixed inset-0 z-50 bg-black/80 flex items-center justify-center"
          onClick={() => setOpenImage(null)}
        >
          <div className="relative w-[90vw] h-[80vh]">
            <Image
              src={openImage}
              alt="Zoomed Image"
              fill
              className="object-contain rounded-xl shadow-2xl"
            />
          </div>
        </div>
      )}

      <div
        className="pt-4 sm:pt-8 border-t border-neutral-200 mb-12 flex flex-col items-center justify-center gap-4"
        id="trustBy"
      >
        <TrustBy />

        <Link href="/login?tab=signUp">
          <Button className="px-24 py-8 text-lg" size="lg" variant="default">
            {t("signUpForFree")}
          </Button>
        </Link>
      </div>

      <FooterForNonLogin />
    </div>
  );
}
