"use client";

import { useTranslations } from "next-intl";
import FeatureTemplatePage from "../feature/FeatureTemplate";

export default function SolutionPage() {
  const t = useTranslations("SolutionPage");

  const sections = [
    {
      label: t("section5Label"),
      anchor: "caprate-tool",
      photo: "/assets/solution/caprate.png",
      title: t("section5Title"),
      salesPoint: [
        t("section5Point1"),
        t("section5Point2"),
        t("section5Point3"),
        t("section5Point4"),
      ],
    },
    {
      label: t("section1Label"),
      anchor: "search-bar",
      photo: "/assets/solution/1.png",
      title: t("section1Title"),
      salesPoint: [
        t("section1Point1"),
        t("section1Point2"),
        t("section1Point3"),
        t("section1Point4"),
      ],
    },
    {
      label: t("section2Label"),
      anchor: "report-builder",
      photo: "/assets/solution/2.png",
      title: t("section2Title"),
      salesPoint: [
        t("section2Point1"),
        t("section2Point2"),
        t("section2Point3"),
        t("section2Point4"),
      ],
    },
    {
      label: t("section3Label"),
      anchor: "shortterm-cf",
      photo: "/assets/solution/3.png",
      title: t("section3Title"),
      salesPoint: [
        t("section3Point1"),
        t("section3Point2"),
        t("section3Point3"),
        t("section3Point4"),
      ],
    },
    {
      label: t("section4Label"),
      anchor: "bidding",
      photo: "/assets/solution/4.png",
      title: t("section4Title"),
      salesPoint: [
        t("section4Point1"),
        t("section4Point2"),
        t("section4Point3"),
        t("section4Point4"),
      ],
    },
  ];

  const heroSectionTitle = t("heroTitle");

  const heroSectionDescriptions = [
    t("heroDescription1"),
    t("heroDescription2"),
    t("heroDescription3"),
    t("heroDescription4"),
  ];

  const heroSectionImage = "/assets/solution/solution.svg";
  const title = t("title");
  const description = t("description");

  return (
    <FeatureTemplatePage
      title={title}
      description={description}
      heroSectionTitle={heroSectionTitle}
      heroSectionDescriptions={heroSectionDescriptions}
      heroSectionImage={heroSectionImage}
      sections={sections}
    />
  );
}
