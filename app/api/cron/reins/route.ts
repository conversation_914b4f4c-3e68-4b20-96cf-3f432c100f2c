import { LARK_URLS, sendLark } from "@/lib/thirdParty/lark";
import { prisma } from "@/lib/prisma";
import dayjsWithTz from "@/lib/thirdParty/dayjsWithTz";
import { NextResponse } from "next/server";
import {
  getChromeBrowser,
  getPageWithRandomUserAgent,
  withPageRecreation,
  recreatePageIfNeeded,
  isPageValid,
} from "@/lib/thirdParty/chromeBrowser";
import { getDataForOneBlock } from "./getDataForOneBlock";
import { saveUserLambdaResult } from "./saveUserLambdaResult";
import { setPageForFilter } from "./utility/setPageForFilter";
import duration from "dayjs/plugin/duration";
import { logger } from "@/lib/logger";
dayjsWithTz.extend(duration);
import { login } from "./utility/reinsLogin";
import { REINS_TASKS, ReinsTaskConstantProps } from "./constants/reinsTasks";

export async function GET(req: Request): Promise<NextResponse> {
  if (
    req.headers.get("Authorization") !==
    `Bearer ${process.env.API_WHITELIST_TOKEN}`
  ) {
    return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
  }

  if (req.method !== "GET") {
    return NextResponse.json(
      { message: "Method Not Allowed" },
      { status: 405 },
    );
  }

  const url = new URL(req.url); // 创建 URL 对象
  const recordType = url.searchParams.get("recordType"); // 获取查询参数
  const account = url.searchParams.get("account"); // 获取查询参数
  const taskIndexFromUrl = url.searchParams.get("taskIndex"); // 获取查询参数
  logger.debug(
    "🔥 url.searchParams, recordType, yday, account",
    recordType,
    account,
  );

  if (recordType === undefined || recordType === null) {
    return NextResponse.json(
      { message: "Record type is required" },
      { status: 400 },
    );
  }

  let browser;
  let page;

  let currentMinute = dayjsWithTz().minute(); // Job start at 15,30,45,00
  let taskIndexForCurrentTime = Math.floor(currentMinute / 15);

  logger.debug(
    "🔥 taskIndex for using is",
    taskIndexForCurrentTime,
    ", currentMinute is",
    currentMinute,
  );

  try {
    let reinsTasks = [] as ReinsTaskConstantProps[];

    if (taskIndexFromUrl !== null && taskIndexFromUrl !== undefined) {
      reinsTasks = REINS_TASKS[
        recordType as keyof typeof REINS_TASKS
      ].tasks.filter(
        (task: any) => task.taskIndex === parseInt(taskIndexFromUrl),
      );
    } else {
      logger.debug("🔥 No taskId, all tasks for ", taskIndexForCurrentTime);
      reinsTasks = REINS_TASKS[
        recordType as keyof typeof REINS_TASKS
      ].tasks.filter((task: any) => task.taskIndex === taskIndexForCurrentTime);
    }

    if (reinsTasks === undefined || reinsTasks.length === 0) {
      return NextResponse.json(
        { message: "No task to conduct from lambda, quitting ... " },
        { status: 400 },
      );
    }

    browser = await getChromeBrowser();

    let currentTaskIndex = 0;
    let retries = 3;

    let currentHour = dayjsWithTz().hour();
    logger.debug("🔥 currentHour is", currentHour);

    if (currentHour === 4 || currentHour === 13) {
      reinsTasks = [
        ...reinsTasks.map((task) => ({
          ...task,
          yday: 1,
        })),
        ...reinsTasks,
      ];
    }

    let startTime, endTime;
    let isloggedIn = false;

    for (const reinsTask of reinsTasks) {
      startTime = dayjsWithTz();
      currentTaskIndex += 1;

      for (let i = 0; i < retries; i++) {
        let createdRecordsCount = 0;
        let createdRecordsS3SavedCount = 0;
        let updatedRecordsCount = 0;
        let totalRecordsCount = 0;

        try {
          if (!browser) {
            browser = await getChromeBrowser();
          }

          // FIX 20250303: mvoe it in, because if not in then retry always fail
          if (!page) {
            page = await getPageWithRandomUserAgent(browser);
          }

          logger.info(
            `🔥[${i < retries - 1 ? "First" : "Retry"}] task ${currentTaskIndex}/${reinsTasks.length}`,
            ", task name is ",
            reinsTask.title,
          );

          // 🔥 增加延迟以减少服务器压力
          await new Promise((resolve) =>
            setTimeout(resolve, 2000 + Math.random() * 1000),
          ); // 2-3秒随机延迟

          let criteria = reinsTask["url"];
          logger.debug(
            `🔥 [REINS] Getting data for ${JSON.stringify(criteria)}`,
          );

          if (!isloggedIn) {
            // Do it here once only, else do it only in the catch loop
            await login({
              page,
              account,
            });
            isloggedIn = true;
          }

          await new Promise((resolve) => setTimeout(resolve, 2000));

          // 🔥 Use enhanced page recreation for navigation
          const navigationResult = (await withPageRecreation(
            browser,
            page,
            async (validPage) => {
              await validPage.goto(
                "https://system.reins.jp/main/KG/GKG003100",
                {
                  waitUntil: "networkidle2",
                  timeout: 60000, // Increased timeout
                },
              );
              return validPage;
            },
            "navigation_to_search_page",
            3, // Max retries
          )) as any;

          page = navigationResult.page;

          // Step 1.1 Go to search page
          const searchButtonSelector =
            ".card:nth-child(2) > div > div > div:nth-child(1) button";
          await page.waitForSelector(searchButtonSelector);
          await page.click(searchButtonSelector);
          logger.debug("🔥 Go to search page ..");
          await new Promise((resolve) => setTimeout(resolve, 2000));

          await setPageForFilter(
            criteria,
            page,
            reinsTask,
            reinsTask.yday === 1,
          );

          await page.click(".p-frame-bottom div:nth-child(4) .btn-block.px-0");
          const RESULT_COUNT_SELECTOR = ".nav-item > a";

          await page.waitForSelector(RESULT_COUNT_SELECTOR);

          // e.g. 売土地(117件)
          const PAGINATION_COUNT = 50;
          // Extract the number inside parentheses (e.g., "売土地(117件)")
          totalRecordsCount = await page.evaluate((sel: any) => {
            const element = document.querySelector(sel);
            if (!element) return 0; // If selector is not found, return 0

            const text = element.textContent?.trim() ?? "";
            // eg1: 売外全(住宅以外建物全部)(107件)
            // eg2: 売土地(107件)
            const match = text.match(/\((\d+)件\)/);

            return match ? parseInt(match[1], 10) : 0; // Convert to integer
          }, RESULT_COUNT_SELECTOR);

          const pageCount = Math.ceil(totalRecordsCount / PAGINATION_COUNT);
          logger.info(
            `🔥 Total result count is ${totalRecordsCount}, with ${pageCount} pages`,
          );

          for (let pageIndex = 1; pageIndex <= pageCount; pageIndex++) {
            await page.waitForSelector(".p-table-body");
            const BLOCK_SELECTOR = ".p-table-body-row:nth-child(INDEX)";
            const listLength = await page.evaluate(
              (sel: string) => document.querySelectorAll(sel).length,
              BLOCK_SELECTOR.replace(":nth-child(INDEX)", ""),
            );

            logger.debug("🔥 listLength for target block is: ", listLength);

            for (let i = 1; i <= listLength; i += 1) {
              logger.info(`-----🔥 Processing ${i} / ${listLength} 🔥------`);

              try {
                // 🔥 Use enhanced page recreation for record processing
                const recordResult = (await withPageRecreation(
                  browser,
                  page,
                  async (validPage) => {
                    return await getDataForOneBlock({
                      recordType,
                      page: validPage,
                      index: i,
                      getChirashi: reinsTask.getChirashi,
                      getDetailsForNewRecord: reinsTask.getDetailsForNewRecord,
                    });
                  },
                  `record_processing_${i}`,
                  2, // Max retries for individual records
                )) as any;

                page = recordResult.page;
                const currentRecord = recordResult.result;

                if (currentRecord !== null) {
                  const [a, b, c] = await saveUserLambdaResult({
                    recordType,
                    currentRecord,
                    soldOnly: false,
                  });
                  createdRecordsCount += a;
                  createdRecordsS3SavedCount += b;
                  updatedRecordsCount += c;
                }

                // 🔥 每个记录之间添加延迟以减少资源压力
                await new Promise((resolve) =>
                  setTimeout(resolve, 800 + Math.random() * 400),
                );

                // 🔥 每10个记录强制垃圾回收
                if (i % 10 === 0 && global.gc) {
                  global.gc();
                }
              } catch (recordError: any) {
                const isFrameError =
                  recordError.message &&
                  recordError.message.includes("detached Frame");
                logger.error(
                  `🔥 Error processing record ${i}${isFrameError ? " (Frame detached)" : ""}:`,
                  recordError,
                );

                // 🔥 If frame detached, try to recreate page
                if (isFrameError) {
                  try {
                    await page.close().catch(() => {}); // Ignore close errors
                    page = await getPageWithRandomUserAgent(browser);
                    logger.info(
                      "🔥 Successfully recreated page after frame detachment",
                    );
                  } catch (recreateError) {
                    logger.error("🔥 Failed to recreate page:", recreateError);
                    throw recreateError; // This will trigger the outer retry mechanism
                  }
                }

                // 🔥 记录错误时增加额外延迟
                await new Promise((resolve) => setTimeout(resolve, 3000));
              }
            }

            if (pageIndex < pageCount) {
              logger.debug("🔥 Go to next page ..");

              // 🔥 页面切换重试机制
              let pageChangeSuccess = false;
              for (let pageRetry = 0; pageRetry < 3; pageRetry++) {
                try {
                  await page.click(".row:nth-child(1) .p-pagination-next-icon");
                  await new Promise((resolve) =>
                    setTimeout(resolve, 2000 + Math.random() * 1000),
                  );
                  pageChangeSuccess = true;
                  break;
                } catch (pageError: any) {
                  logger.warn(
                    `🔥 Page change attempt ${pageRetry + 1} failed:`,
                    pageError.message,
                  );
                  if (pageRetry < 2) {
                    await new Promise((resolve) => setTimeout(resolve, 2000));
                  }
                }
              }

              if (!pageChangeSuccess) {
                logger.error(
                  "🔥 Failed to change page after 3 attempts, breaking pagination loop",
                );
                break;
              }
            }
          }

          break; // Do not trigge retry
        } catch (err: any) {
          logger.error("🔥 Error in cron job 🔥", err);

          // 🔥 检查是否是资源不足错误
          const isResourceError =
            err.message &&
            (err.message.includes("ERR_INSUFFICIENT_RESOURCES") ||
              err.message.includes("net::ERR_INSUFFICIENT_RESOURCES") ||
              err.message.includes("Protocol error") ||
              err.message.includes("Target closed") ||
              err.message.includes("Session closed"));

          await sendLark({
            message:
              `❌${i >= 1 ? "❌" : ""}${i >= 2 ? "❌" : ""}[${reinsTask.title}]${reinsTask.yday === 1 ? "[YDAY]" : ""}[${recordType}-${currentTaskIndex}/${reinsTasks.length}]${isResourceError ? "[RESOURCE_ERROR]" : ""}` +
              err,
            url: LARK_URLS.BOT_SHUUKEI_CHANNEL,
          });

          // 🔥 资源错误时增加更长的等待时间
          const waitTime = isResourceError ? 10000 : 5000;
          await new Promise((resolve) => setTimeout(resolve, waitTime));

          // 🔥 强制清理所有资源
          try {
            if (page) {
              await page.close();
              page = null;
            }
          } catch (pageCloseError) {
            logger.error("🔥 Error closing page:", pageCloseError);
          }

          try {
            if (browser) {
              await browser.close();
              browser = null; // Ensure a fresh browser instance is used next retry
            }
          } catch (browserCloseError) {
            logger.error("🔥 Error closing browser:", browserCloseError);
          }

          // 🔥 清理临时文件和缓存
          try {
            const { exec } = require("child_process");
            exec(
              "rm -rf /tmp/chrome-cache/* 2>/dev/null || true",
              (error: any) => {
                if (error) {
                  logger.debug("🔥 Cache cleanup warning:", error.message);
                }
              },
            );
          } catch (cleanupError) {
            logger.debug("🔥 Cache cleanup error:", cleanupError);
          }

          // 🔥 强制垃圾回收
          if (global.gc) {
            global.gc();
          }

          isloggedIn = false;
        } finally {
          endTime = dayjsWithTz();
          const duration = dayjsWithTz.duration(endTime.diff(startTime));

          // [${dayjsWithTz().tz('Asia/Tokyo').format("YYYY-MM-DD HH:mm:ss")}]
          const message = `✅[${reinsTask.title}][🕒${duration.minutes()}:${duration.seconds()}]${reinsTask.yday === 1 ? "[YDAY]" : ""}[${recordType}-${currentTaskIndex}/${reinsTasks.length}]${totalRecordsCount} total | ${updatedRecordsCount} updated | ${createdRecordsCount} created | ${createdRecordsS3SavedCount} s3 saved`;
          logger.debug(message);
          await sendLark({
            message,
            url: LARK_URLS.BOT_SHUUKEI_CHANNEL,
          });
        }
      }
    }

    return NextResponse.json({
      status: "success",
    });
  } catch (error) {
    logger.error("🔥 Error in cron job 🔥", error);
    await sendLark({
      message: `[Error reins getting data]` + error,
      url: LARK_URLS.BOT_SHUUKEI_CHANNEL,
    });

    return NextResponse.json({ message: "error", error: error });
  } finally {
    logger.debug("🔄 Closing page and browser!");
    if (page) {
      await page.close();
    }

    if (browser) {
      await browser.close();
    }

    await prisma.$disconnect(); // prevent long hanging connection
  }
}
